# GAT侧边栏功能详细文档

**版本**: 2.0
**更新日期**: 2024年12月
**适用于**: Kylin Robot IDE

---

## 📋 目录

1. [概述](#概述)
2. [GAT侧边栏结构](#gat侧边栏结构)
3. [测试集视图功能](#测试集视图功能)
4. [测试用例视图功能](#测试用例视图功能)
5. [公共方法视图功能](#公共方法视图功能)
6. [编辑器集成功能](#编辑器集成功能)
7. [录制与回放功能](#录制与回放功能)
8. [配置与设置](#配置与设置)
9. [快捷键与命令](#快捷键与命令)
10. [故障排除](#故障排除)

---

## 🎯 概述

GAT（GUI Automation Testing）是集成在Kylin Robot IDE中的自动化测试框架，提供完整的GUI测试解决方案。GAT侧边栏是用户与测试功能交互的主要界面，包含三个核心视图和丰富的功能模块。

### 核心特性
- 🎬 **可视化测试用例管理** - 直观的测试集和测试用例组织
- 🎥 **智能录制回放** - 自动化的用户操作录制与精确回放
- 🔧 **公共方法库** - 可重用的测试方法和组件
- 📝 **代码智能提示** - YAML文件的实时语法提示和错误检查
- 🎨 **可视化编辑** - 图形化的测试用例编辑界面
- 🔗 **禅道集成** - 与禅道项目管理系统的无缝集成

---

## 🏗️ GAT侧边栏结构

GAT侧边栏位于IDE的主侧边栏中，包含三个主要视图：

```
GAT 侧边栏
├── 📁 测试集视图 (Test Set View)
├── 📄 测试用例视图 (Test Case View)
└── 🔧 公共方法视图 (Common Method View)
```

### 视图容器特性
- **图标**: Dashboard图标 (仪表板)
- **位置**: 主侧边栏第8位
- **可见性**: 支持隐藏/显示切换
- **布局**: 支持视图重新排列和拖拽

---

## 📁 测试集视图功能

### 主要功能

#### 1. 测试集操作
- **打开测试集** (`gat.openTestSet`)
  - 浏览并选择现有测试集
  - 支持GAT和OCR两种类型
  - 自动解析测试集配置文件
  - 智能路径推断和验证

- **新建测试集** (`gat.newTestSet`)
  - 创建新的测试集项目
  - 支持应用程序自动识别
  - 生成标准目录结构
  - 自动配置测试环境

#### 2. 测试集管理
- **测试集信息显示**
  - 当前选中测试集名称
  - 测试用例数量统计
  - 测试集类型标识（GAT/OCR）
  - 最后修改时间

- **测试集切换**
  - 快速切换不同测试集
  - 保持测试集状态
  - 自动刷新相关视图

#### 3. 设置与配置
- **测试集设置**
  - 配置测试用例路径 (`gat.testcasePath`)
  - 自动路径推断
  - 工作区集成设置

- **远程桌面显示** (`kylin-robot-ide.openRemoteDisplay`)
  - 打开远程显示窗口
  - 支持远程测试环境
  - 实时屏幕共享

### 使用流程

1. **首次使用**
   ```
   点击"测试集操作" → 选择"新建测试集" → 输入应用信息 → 自动生成项目结构
   ```

2. **打开现有测试集**
   ```
   点击"测试集操作" → 选择"打开测试集" → 浏览选择测试集目录 → 自动加载
   ```

3. **配置测试路径**
   ```
   点击设置图标 → 选择"测试集设置" → 配置testcase路径 → 保存设置
   ```

---

## 📄 测试用例视图功能

### 主要功能

#### 1. 测试用例管理
- **测试用例列表**
  - 显示当前测试集的所有测试用例
  - 支持ID、名称、描述显示
  - 实时状态更新
  - 智能排序和筛选

- **添加测试用例**
  - 可视化测试用例创建界面
  - 支持基本信息录入
  - 自动生成YAML文件
  - 集成到测试集配置

#### 2. 测试用例操作
- **录制功能**
  - 一键开始录制用户操作
  - 智能控件识别和捕获
  - 实时录制状态显示
  - 支持暂停/恢复录制

- **回放功能**
  - 精确回放录制的操作
  - 支持步骤级调试
  - 错误检测和报告
  - 执行结果统计

- **编辑功能**
  - 直接编辑YAML测试文件
  - 语法高亮和错误检查
  - 智能代码补全
  - 实时预览效果

#### 3. 测试用例状态
- **录制状态管理**
  - 当前录制用例标识
  - 录制进度显示
  - 状态变化通知
  - 录制工具栏集成

### 操作指南

#### 创建新测试用例
1. 点击"添加测试用例"按钮
2. 填写测试用例基本信息：
   - 测试用例ID（唯一标识）
   - 测试用例名称
   - 测试模块
   - 录制类型（直接录制/手动编写）
   - 是否需要人工校验
3. 选择驱动应用程序
4. 点击"创建"生成测试用例文件

#### 录制测试用例
1. 选择目标测试用例
2. 点击"录制"按钮
3. 系统自动最小化IDE窗口
4. 在目标应用中执行测试操作
5. 点击录制工具栏的"停止"按钮
6. 自动保存录制结果到YAML文件

#### 回放测试用例
1. 选择要回放的测试用例
2. 点击"播放"按钮
3. 系统自动执行测试步骤
4. 在输出面板查看执行日志
5. 检查测试结果和断言

---

## 🔧 公共方法视图功能

### 主要功能

#### 1. 方法库管理
- **YAML方法文件加载**
  - 自动扫描公共方法目录
  - 解析YAML格式的方法定义
  - 分类显示方法库
  - 支持文件夹折叠/展开

- **方法信息展示**
  - 方法名称和描述
  - 参数列表和类型
  - 使用示例和说明
  - 方法分类和标签

#### 2. 方法插入功能
- **智能方法插入**
  - 拖拽插入到编辑器
  - 自动参数模板生成
  - 上下文感知插入
  - 缩进自动调整

- **参数配置**
  - 可视化参数编辑界面
  - 参数类型验证
  - 默认值设置
  - 参数依赖关系处理

#### 3. 方法搜索
- **快速搜索**
  - 按方法名搜索
  - 按功能分类筛选
  - 模糊匹配支持
  - 搜索结果高亮

### 使用方式

#### 浏览公共方法
1. 在公共方法视图中浏览方法分类
2. 点击文件夹展开查看方法列表
3. 悬停查看方法详细信息
4. 点击方法名查看完整文档

#### 插入公共方法
1. 在编辑器中定位到插入位置
2. 在公共方法视图中选择目标方法
3. 双击或拖拽方法到编辑器
4. 在弹出的参数配置窗口中设置参数
5. 点击"插入"完成方法插入

---

## 📝 编辑器集成功能

### 右键菜单功能

Robot在编辑器右键菜单中添加了专门的子菜单，提供以下功能：

#### 1. 编辑Action (`gat.editAction`)
- **触发条件**: 光标位于action块中时可见
- **功能**:
  - 自动识别当前action块
  - 提取方法名和参数
  - 打开可视化编辑窗口
  - 支持参数修改和验证
  - 一键更新到原文件

#### 2. 插入公共方法 (`gat.insertCommonMethod`)
- **触发条件**: 在YAML文件中任意位置
- **功能**:
  - 弹出公共方法选择窗口
  - 智能检测插入上下文
  - 自动处理缩进和格式
  - 支持setup/teardown/步骤块插入

#### 3. 修复Key (`gat.fixKey`)
- **触发条件**: 光标位于key字段时可见
- **功能**:
  - 自动检测driver信息
  - 打开控件捕获窗口
  - 实时预览控件信息
  - 一键修复key值

### 智能提示功能

#### 1. Action悬停提示
- **功能**: 鼠标悬停在action名称上时显示详细信息
- **内容**:
  - Action功能描述
  - 参数列表和说明
  - 使用示例
  - 相关文档链接

#### 2. 语法检查
- **实时检查**: YAML语法错误检测
- **智能提示**: 参数类型验证
- **错误标记**: 红色波浪线标记错误位置
- **修复建议**: 提供自动修复选项

#### 3. 代码补全
- **Action名称补全**: 输入时自动提示可用action
- **参数补全**: 自动补全参数名称和值
- **路径补全**: 文件路径自动补全
- **变量补全**: 测试变量自动补全

---

## 🎥 录制与回放功能

### 录制工具栏

录制开始时会显示浮动工具栏，提供以下功能：

#### 1. 录制控制
- **暂停/恢复**: 临时暂停录制过程
- **停止录制**: 结束录制并保存结果
- **状态显示**: 实时显示录制状态和时间

#### 2. 方法插入
- **插入公共方法**: 录制过程中插入预定义方法
- **自定义操作**: 添加自定义验证步骤
- **注释添加**: 为录制步骤添加说明

#### 3. 控件捕获
- **智能识别**: 自动识别界面控件
- **属性提取**: 提取控件关键属性
- **定位策略**: 生成稳定的定位表达式

### 回放引擎

#### 1. 执行控制
- **步骤级执行**: 支持单步调试
- **断点设置**: 在指定步骤暂停
- **错误处理**: 智能错误恢复机制
- **超时控制**: 可配置的操作超时

#### 2. 结果验证
- **断言检查**: 自动执行断言验证
- **截图对比**: 界面变化对比
- **数据验证**: 输出数据正确性检查
- **性能监控**: 执行时间和资源使用

#### 3. 报告生成
- **执行日志**: 详细的执行步骤记录
- **错误报告**: 失败原因和位置
- **统计信息**: 执行时间和成功率
- **截图记录**: 关键步骤的界面截图

---

## ⚙️ 配置与设置

### 主要配置项

#### 1. 测试用例路径 (`gat.testcasePath`)
- **类型**: 字符串
- **默认值**: 空（自动推断）
- **描述**: 指定KylinRobot-v2测试用例目录的绝对路径
- **示例**: `/path/to/extensions/KylinRobot-v2/testcase`

#### 2. 录制设置
- **自动最小化**: 录制时是否自动最小化IDE
- **录制质量**: 控件识别精度设置
- **超时设置**: 操作等待超时时间
- **截图设置**: 是否保存操作截图

#### 3. 回放设置
- **执行速度**: 回放操作的执行间隔
- **错误处理**: 遇到错误时的处理策略
- **日志级别**: 执行日志的详细程度
- **并发设置**: 是否支持并发执行

### 配置方法

#### 1. 通过界面配置
```
GAT侧边栏 → 设置图标 → 测试集设置 → 修改配置 → 保存
```

#### 2. 通过设置文件
```json
{
  "gat.testcasePath": "/path/to/testcase",
  "gat.recording.autoMinimize": true,
  "gat.playback.speed": "normal"
}
```

#### 3. 通过命令面板
```
Ctrl+Shift+P → 输入"GAT设置" → 选择配置项 → 修改值
```

---

## ⌨️ 快捷键与命令

### 主要命令

| 命令ID | 功能描述 | 默认快捷键 |
|--------|----------|------------|
| `gat.openTestSet` | 打开测试集 | - |
| `gat.newTestSet` | 新建测试集 | - |
| `gat.editAction` | 编辑Action | - |
| `gat.insertCommonMethod` | 插入公共方法 | - |
| `gat.fixKey` | 修复Key | - |
| `kylin-robot-ide.openRemoteDisplay` | 打开远程显示 | - |

### 上下文菜单

#### 编辑器右键菜单
- **Robot** 子菜单
  - 编辑Action（仅在action块中可见）
  - 插入公共方法
  - 修复Key（仅在key字段可见）

#### 侧边栏右键菜单
- **测试集操作**
  - 打开测试集
  - 新建测试集
- **设置选项**
  - 测试集设置
  - 远程桌面显示

### 自定义快捷键

用户可以通过以下方式自定义快捷键：

1. 打开命令面板 (`Ctrl+Shift+P`)
2. 输入"首选项：打开键盘快捷方式"
3. 搜索GAT相关命令
4. 点击命令旁的"+"号添加快捷键
5. 输入期望的快捷键组合

---

## 🔧 故障排除

### 常见问题

#### 1. 测试集无法加载
**症状**: 侧边栏显示"未选择测试集"或加载失败

**解决方案**:
1. 检查测试用例路径配置是否正确
2. 确认目录权限是否足够
3. 验证测试集配置文件格式
4. 重启IDE并重新加载

#### 2. 录制功能无响应
**症状**: 点击录制按钮后无反应或录制工具栏不显示

**解决方案**:
1. 检查是否有其他录制任务正在进行
2. 确认目标应用是否正常运行
3. 重启录制服务
4. 检查系统权限设置

#### 3. 公共方法无法加载
**症状**: 公共方法视图为空或显示加载错误

**解决方案**:
1. 检查公共方法文件路径
2. 验证YAML文件格式正确性
3. 确认文件读取权限
4. 清除缓存并重新加载

#### 4. 代码提示不工作
**症状**: 编辑器中没有智能提示或悬停信息

**解决方案**:
1. 确认文件类型为YAML
2. 检查语言服务是否正常
3. 重新加载窗口
4. 更新GAT插件版本

### 调试方法

#### 1. 查看日志
- 打开开发者工具 (`F12`)
- 查看控制台输出
- 搜索GAT相关错误信息

#### 2. 重置配置
```javascript
// 在开发者控制台中执行
localStorage.removeItem('gat.settings');
location.reload();
```

#### 3. 清除缓存
```bash
# 清除GAT缓存文件
rm -rf ~/.config/kylin-robot-ide/gat-cache/
```

### 获取支持

如果问题仍然存在，请：

1. 收集错误日志和配置信息
2. 记录重现步骤
3. 联系技术支持团队
4. 提供系统环境信息

---

## 📚 附录

### 相关文档
- [GAT API参考文档](./GAT_API_Reference.md)
- [测试用例编写指南](./TestCase_Writing_Guide.md)
- [公共方法开发指南](./Common_Method_Development.md)

### 更新历史
- **v2.0** (2024-12): 新增可视化编辑功能，优化用户界面
- **v1.5** (2024-10): 增强录制回放功能，添加智能提示
- **v1.0** (2024-08): 初始版本发布

### 技术支持
- **邮箱**: <EMAIL>
- **文档**: https://docs.kylinrobot.com/gat
- **社区**: https://community.kylinrobot.com

---

*本文档将持续更新，请关注最新版本。*
