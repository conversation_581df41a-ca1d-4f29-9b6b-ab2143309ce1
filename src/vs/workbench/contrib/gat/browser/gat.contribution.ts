/*---------------------------------------------------------------------------------------------
 *  Copyright (c) 2023-2024 <PERSON><PERSON><PERSON>. All rights reserved.
 *  Licensed under the MIT License.
 *--------------------------------------------------------------------------------------------*/

import './media/gat.css';
import { localize, localize2 } from '../../../../nls.js';
import { SyncDescriptor } from '../../../../platform/instantiation/common/descriptors.js';
import { ViewPaneContainer } from '../../../browser/parts/views/viewPaneContainer.js';
import { IViewContainersRegistry, Extensions as ViewContainerExtensions, ViewContainer, ViewContainerLocation, IViewsRegistry, IViewDescriptorService } from '../../../common/views.js';
import { Registry } from '../../../../platform/registry/common/platform.js';
import { Codicon } from '../../../../base/common/codicons.js';
import { registerIcon } from '../../../../platform/theme/common/iconRegistry.js';
import { WorkbenchPhase, registerWorkbenchContribution2 } from '../../../common/contributions.js';
// Import configuration registry related items
import { Extensions as ConfigurationExtensions, IConfigurationRegistry, ConfigurationScope } from '../../../../platform/configuration/common/configurationRegistry.js';
import { workbenchConfigurationNodeBase } from '../../../common/configuration.js';
import { Range } from '../../../../editor/common/core/range.js';

// 导入视图组件
import { TestSetView } from './views/testSetView.js';
import { TestCaseView } from './views/testCaseView.js';
import { CommonMethodView } from './views/commonMethodView.js';
import { registerEditorFeature } from './features/actionHoverProvider.js';
import { TestCasePlayer } from './features/testCasePlayer.js';
import { TestCaseRecorder } from './features/testCaseRecorder.js';
import { InsertMethodWindow } from './features/methodWindow/insertMethodWindow.js';
import { FixKeyWindow } from './features/fixKeyWindow/fixKeyWindow.js';
import { GATPathResolver } from './common/pathResolver.js';


import { ILogService } from '../../../../platform/log/common/log.js';
import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
import { IConfigurationService } from '../../../../platform/configuration/common/configuration.js';
import { joinPath } from '../../../../base/common/resources.js';
import { basename } from '../../../../base/common/path.js';
// 确保在Electron环境下注册服务
// 在浏览器环境中导入electron-sandbox模块会被忽略，因此这是安全的
import '../electron-sandbox/zenTaoService.js';
import { CommandsRegistry } from '../../../../platform/commands/common/commands.js';
import { ICommandService } from '../../../../platform/commands/common/commands.js';
import { ILanguageService } from '../../../../editor/common/languages/language.js';
import { IWorkbenchContributionsRegistry, Extensions as WorkbenchExtensions, IWorkbenchContribution } from '../../../common/contributions.js';
import { LifecyclePhase } from '../../../../workbench/services/lifecycle/common/lifecycle.js';
import { URI } from '../../../../base/common/uri.js';
import { IViewsService } from '../../../services/views/common/viewsService.js';
import { MenuRegistry, MenuId } from '../../../../platform/actions/common/actions.js';
import { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';
import { IEditorService } from '../../../services/editor/common/editorService.js';
import { IEditorContribution } from '../../../../editor/common/editorCommon.js';
import { ContextKeyExpr, IContextKeyService, RawContextKey } from '../../../../platform/contextkey/common/contextkey.js';
import { ICodeEditor } from '../../../../editor/browser/editorBrowser.js';
import { registerEditorContribution, EditorContributionInstantiation } from '../../../../editor/browser/editorExtensions.js';
import { DisposableStore } from '../../../../base/common/lifecycle.js';
import { INotificationService } from '../../../../platform/notification/common/notification.js';
import { IFileService } from '../../../../platform/files/common/files.js';
import { VSBuffer } from '../../../../base/common/buffer.js';
import { IPaneCompositePartService } from '../../../services/panecomposite/browser/panecomposite.js';
import { ICustomAuthenticationService } from '../../../../platform/authentication/common/customAuthenticationService.js';

// 定义GAT图标
const gatIcon = registerIcon('gat-view-icon', Codicon.dashboard, localize('gatViewIcon', "Icon for the GAT view."));

// 新增：定义是否在 key 字段的上下文键
const ctxIsKeyField = new RawContextKey<boolean>('gat:isKeyField', false, localize('fixKeyContext', "是否在 key 字段上"));

// 新增：文本编辑器贡献，用于更新 key 字段上下文
class KeyFieldContextContribution implements IEditorContribution {
    static readonly ID = 'gat.keyFieldContext';
    private readonly _disposable = new DisposableStore();
    private readonly _ctxIsKeyField;

    constructor(
        private readonly _editor: ICodeEditor,
        @IContextKeyService contextKeyService: IContextKeyService
    ) {
        this._ctxIsKeyField = ctxIsKeyField.bindTo(contextKeyService);
        // 监听光标和模型变化
        this._disposable.add(this._editor.onDidChangeCursorPosition(() => this.updateContext()));
        this._disposable.add(this._editor.onDidChangeModel(() => this.updateContext()));
        this.updateContext();
    }

    private updateContext() {
        const model = this._editor.getModel();
        const pos = this._editor.getPosition();
        if (!model || !pos) {
            this._ctxIsKeyField.set(false);
            return;
        }
        // 精确检测光标处的单词
        const word = model.getWordAtPosition(pos)?.word;
        if (word === 'key') {
            this._ctxIsKeyField.set(true);
            return;
        }
        // 范围检测所在行是否包含 key: 格式
        const lineContent = model.getLineContent(pos.lineNumber);
        const isKeyLine = /\bkey\s*:/.test(lineContent);
        this._ctxIsKeyField.set(isKeyLine);
    }

    dispose() {
        this._disposable.dispose();
    }
}

// 注册 KeyFieldContextContribution，确保上下文键在编辑器启动时生效
registerEditorContribution(
    KeyFieldContextContribution.ID,
    KeyFieldContextContribution,
    EditorContributionInstantiation.Eager
);

// 新增：定义是否在 action 块的上下文键
const ctxIsActionBlock = new RawContextKey<boolean>('gat:isActionBlock', false, localize('actionBlockContext', "是否在 action 块中"));

// 文本编辑器贡献，用于更新 action 块上下文
class ActionBlockContextContribution implements IEditorContribution {
    static readonly ID = 'gat.actionBlockContext';
    private readonly _disposable = new DisposableStore();
    private readonly _ctxIsActionBlock;

    constructor(
        private readonly _editor: ICodeEditor,
        @IContextKeyService contextKeyService: IContextKeyService
    ) {
        this._ctxIsActionBlock = ctxIsActionBlock.bindTo(contextKeyService);
        this._disposable.add(this._editor.onDidChangeCursorPosition(() => this.updateContext()));
        this._disposable.add(this._editor.onDidChangeModel(() => this.updateContext()));
        this.updateContext();
    }

    private updateContext(): void {
        const model = this._editor.getModel();
        const pos = this._editor.getPosition();
        if (!model || !pos) {
            this._ctxIsActionBlock.set(false);
            return;
        }
        const lineNum = pos.lineNumber;
        const text = model.getLineContent(lineNum);
        // 空行不视为 action 块
        if (text.trim() === '') {
            this._ctxIsActionBlock.set(false);
            return;
        }
        const currIndent = (text.match(/^\s*/)?.[0].length) || 0;
        let actionLine = -1;
        let actionIndent = 0;
        // 向上查找 action 定义行
        for (let i = lineNum; i >= 1; i--) {
            const ln = model.getLineContent(i);
            const trimmed = ln.trim();
            if (/^-\s*action:/.test(trimmed)) {
                actionLine = i;
                actionIndent = (ln.match(/^\s*/)?.[0].length) || 0;
                break;
            }
            // 遇到其他同级节点则停止
            if (/^-\s+\S+/.test(trimmed)) {
                break;
            }
        }
        if (actionLine === -1) {
            this._ctxIsActionBlock.set(false);
            return;
        }
        // 方法行本身
        if (lineNum === actionLine) {
            this._ctxIsActionBlock.set(true);
            return;
        }
        // 仅当当前行缩进大于 action 行缩进才视为其内
        this._ctxIsActionBlock.set(currIndent > actionIndent);
    }

    dispose(): void {
        this._disposable.dispose();
    }
}

// 注册 ActionBlockContextContribution
registerEditorContribution(
    ActionBlockContextContribution.ID,
    ActionBlockContextContribution,
    EditorContributionInstantiation.Eager
);

// 定义视图容器
const GAT_VIEW_CONTAINER_ID = 'workbench.view.gat';
const GAT_VIEW_CONTAINER: ViewContainer = Registry.as<IViewContainersRegistry>(ViewContainerExtensions.ViewContainersRegistry).registerViewContainer({
    id: GAT_VIEW_CONTAINER_ID,
    title: localize2('gat', "GAT"),
    icon: gatIcon,
    ctorDescriptor: new SyncDescriptor(ViewPaneContainer, [GAT_VIEW_CONTAINER_ID, { mergeViewWithContainerWhenSingleView: true }]),
    storageId: GAT_VIEW_CONTAINER_ID,
    hideIfEmpty: true,
    order: 8,
}, ViewContainerLocation.Sidebar);

// Register GAT Configuration Setting
const configurationRegistry = Registry.as<IConfigurationRegistry>(ConfigurationExtensions.Configuration);
configurationRegistry.registerConfiguration({
    ...workbenchConfigurationNodeBase,
    'id': 'gat',
    'order': 101,
    'title': localize('gatConfigurationTitle', "GAT"),
    'properties': {
        'gat.testcasePath': {
            'type': 'string',
            'description': localize('gat.testcasePath.description', "指定 KylinRobot-v2 测试用例 (testcase) 目录的绝对路径。如果未设置，将自动使用内置的 KylinRobot-v2 模块或在当前工作区查找。可以指向 'extensions/KylinRobot-v2/testcase' 或 '/path/to/your/testcase' 目录。"),
            'default': '',
            'scope': ConfigurationScope.MACHINE_OVERRIDABLE
        },
        'gat.fileDialogTimeout': {
            'type': 'number',
            'description': localize('gat.fileDialogTimeout.description', "文件对话框的超时时间（毫秒）。设置为 0 表示禁用超时。默认值为 60000（60秒）。"),
            'default': 60000,
            'minimum': 0,
            'scope': ConfigurationScope.MACHINE_OVERRIDABLE
        },
        'gat.userPassword': {
            'type': 'string',
            'description': localize('gat.userPassword.description', "用于Wayland环境下全流程录制的用户密码。如果未设置，启动录制时会弹出密码输入框。"),
            'default': '',
            'scope': ConfigurationScope.MACHINE_OVERRIDABLE
        }
    }
});

// 注册视图 (Moved out of the class)
const viewsRegistry = Registry.as<IViewsRegistry>(ViewContainerExtensions.ViewsRegistry);

// 注册测试集视图
viewsRegistry.registerViews([{
    id: TestSetView.ID,
    name: TestSetView.TITLE,
    containerIcon: gatIcon,
    ctorDescriptor: new SyncDescriptor(TestSetView),
    canToggleVisibility: true,
    canMoveView: true,
    weight: 10,
    order: 1,
}], GAT_VIEW_CONTAINER);

// 注册测试用例视图
viewsRegistry.registerViews([{
    id: TestCaseView.ID,
    name: TestCaseView.TITLE,
    containerIcon: gatIcon,
    ctorDescriptor: new SyncDescriptor(TestCaseView),
    canToggleVisibility: true,
    canMoveView: true,
    weight: 20,
    order: 2
}], GAT_VIEW_CONTAINER);

// 注册公共方法视图
viewsRegistry.registerViews([{
    id: CommonMethodView.ID,
    name: CommonMethodView.TITLE,
    containerIcon: gatIcon,
    ctorDescriptor: new SyncDescriptor(CommonMethodView),
    canToggleVisibility: true,
    canMoveView: true,
    weight: 30,
    order: 3,
}], GAT_VIEW_CONTAINER);

// 注册工作台贡献
// 使用新的API注册工作台贡献
registerWorkbenchContribution2('gat.editorFeature', registerEditorFeature, WorkbenchPhase.AfterRestored);
registerWorkbenchContribution2('gat.testCasePlayer', TestCasePlayer, WorkbenchPhase.AfterRestored);
registerWorkbenchContribution2('gat.testCaseRecorder', TestCaseRecorder, WorkbenchPhase.AfterRestored);

// 注册编辑 Action 命令
CommandsRegistry.registerCommand('gat.editAction', async (accessor) => {
    const instantiationService = accessor.get(IInstantiationService);
    const fileService = accessor.get(IFileService);
    const editorService = accessor.get(IEditorService);
    const control = editorService.activeTextEditorControl;
    if (!control || typeof (control as any).getModel !== 'function') {
        return;
    }
    const editor = control as ICodeEditor;
    const model = editor.getModel();
    const pos = editor.getPosition();
    if (!model || !pos) {
        return;
    }
    // 向上查找 action 行
    let actionLine = -1;
    let actionIndent = 0;
    // 用于记录参数块结束行，初始化为 actionLine
    let endLine = actionLine;
    for (let i = pos.lineNumber; i >= 1; i--) {
        const line = model.getLineContent(i);
        const trimmed = line.trim();
        if (/^-\s*action:/.test(trimmed)) {
            actionLine = i;
            actionIndent = (line.match(/^\s*/)?.[0].length) || 0;
            break;
        }
        if (/^-\s+\S+/.test(trimmed)) {
            break;
        }
    }
    if (actionLine === -1) {
        return;
    }
    // 提取 methodName
    const first = model.getLineContent(actionLine).trim();
    const m = first.match(/^-\s*action:\s*(\S+)/);
    const methodName = m ? m[1] : '';
    // 提取参数块 YAML
    const total = model.getLineCount();
    for (let i = actionLine + 1; i <= total; i++) {
        const ln = model.getLineContent(i);
        const indent = (ln.match(/^\s*/)?.[0].length) || 0;
        if (ln.trim() === '' || indent > actionIndent) {
            endLine = i;
        } else {
            break;
        }
    }
    const params: string[] = [];
    for (let i = actionLine + 1; i <= endLine; i++) {
        const ln = model.getLineContent(i);
        params.push(ln.slice(actionIndent + 2));
    }
    let paramYaml = params.join('\n');
    // 移除参数包装层 'kwargs:'
    const yamlLines = paramYaml.split('\n');
    if (yamlLines[0]?.trim() === 'kwargs:') {
        yamlLines.shift();
        paramYaml = yamlLines.join('\n');
    }

    // 调试：打印定位信息
    console.info(`编辑 Action 定位: actionLine=${actionLine}, endLine=${endLine}`);
    // 打开插入窗口并传递原始行号
    const win = instantiationService.createInstance(InsertMethodWindow);
    const fileUriStr = model.uri.toString();
    win.setEditContext(methodName, paramYaml, fileUriStr, actionLine, endLine);
    // 监听编辑确认事件，执行文件替换
    win.onMethodInserted(async ({ methodName: mName, originalYAML, isEdit, testCaseFileUri, originalStartLine, originalEndLine }) => {
        if (!isEdit || !originalYAML) { return; }
        console.info(`onMethodInserted 调试: originalStartLine=${originalStartLine}, originalEndLine=${originalEndLine}`);
        const formattedCode = win.eventHandler.getFormattedParameters() || '';
        // 确定操作的文件 URI
        const fileUri = testCaseFileUri ? URI.parse(testCaseFileUri) : model.uri;
        try {
            const content = (await fileService.readFile(fileUri)).value.toString();
            const lines = content.split(/\r?\n/);
            if (originalStartLine != null && originalEndLine != null) {
                // 1-based to 0-based
                const startIdx = originalStartLine - 1;
                const endIdx = originalEndLine;
                const indent = lines[startIdx].search(/\S/);
                const newBlock = formattedCode.split('\n').map(l => ' '.repeat(indent) + l);
                lines.splice(startIdx, endIdx - startIdx + 1, ...newBlock);
                console.info(`基于行号替换 ${fileUri.toString()} [${originalStartLine}-${originalEndLine}]`);
            } else {
                console.warn('缺少原始行号，无法精确替换');
                return;
            }
            await fileService.writeFile(fileUri, VSBuffer.fromString(lines.join('\n')));
            console.info(`已替换文件 ${fileUri.toString()} 中方法 ${mName}`);
        } catch (err) {
            console.error('编辑 Action 写入失败:', err);
        }
    });
    await win.show();
});

// 注册打开远程显示命令
CommandsRegistry.registerCommand('kylin-robot-ide.openRemoteDisplay', async (accessor) => {
    console.info('[GAT] 执行命令 kylin-robot-ide.openRemoteDisplay');
    try {
        const commandService = accessor.get(ICommandService);
        await commandService.executeCommand('remoteDisplay.vncConnect');
        console.info('[GAT] 已成功调用远程显示VNC连接命令');
    } catch (error) {
        console.error('[GAT] 打开远程显示失败:', error);
    }
});

// 注册切换到Explorer视图的命令（并隐藏GAT）
CommandsRegistry.registerCommand('gat.switchToExplorer', async (accessor) => {
    const paneCompositeService = accessor.get(IPaneCompositePartService);
    const viewDescriptorService = accessor.get(IViewDescriptorService);
    try {
        // 将GAT容器移动到面板位置来"隐藏"它
        viewDescriptorService.moveViewContainerToLocation(GAT_VIEW_CONTAINER, ViewContainerLocation.Panel);

        // 切换到Explorer视图
        await paneCompositeService.openPaneComposite('workbench.view.explorer', ViewContainerLocation.Sidebar, true);
        console.info('[GAT] 已切换到Explorer视图，GAT已隐藏');
    } catch (error) {
        console.error('[GAT] 切换到Explorer视图失败:', error);
    }
});

// 注册从Explorer切换回GAT视图的命令（并显示GAT）
CommandsRegistry.registerCommand('gat.switchToGAT', async (accessor) => {
    const paneCompositeService = accessor.get(IPaneCompositePartService);
    const viewDescriptorService = accessor.get(IViewDescriptorService);
    try {
        // 将GAT容器移回侧边栏位置来"显示"它
        viewDescriptorService.moveViewContainerToLocation(GAT_VIEW_CONTAINER, ViewContainerLocation.Sidebar);

        // 切换到GAT视图
        await paneCompositeService.openPaneComposite('workbench.view.gat', ViewContainerLocation.Sidebar, true);
        console.info('[GAT] 已切换到GAT视图，GAT已显示');
    } catch (error) {
        console.error('[GAT] 切换到GAT视图失败:', error);
    }
});

// 定义编辑器上下文的 GAT 子菜单 ID
const GAT_EDITOR_CONTEXT_MENU = MenuId.for('EditorContext/GAT');

// 在编辑器右键菜单添加 Robot 子菜单
MenuRegistry.appendMenuItem(MenuId.EditorContext, {
    submenu: GAT_EDITOR_CONTEXT_MENU,
    title: localize('gatContextMenuLabel', "Robot"),
    group: 'navigation',
    order: 100
});

// 在 GAT 子菜单下添加"编辑 Action"菜单项，仅当处于 action 块时可见
MenuRegistry.appendMenuItem(GAT_EDITOR_CONTEXT_MENU, {
    command: { id: 'gat.editAction', title: localize('editAction', "编辑 Action") },
    group: '1_gat',
    order: 0,
    when: ContextKeyExpr.equals('gat:isActionBlock', true)
});

// 注册"插入公共方法"命令：始终弹出插入方法窗口，并将选中方法插入当前编辑器
CommandsRegistry.registerCommand('gat.insertCommonMethod', async (accessor) => {
    const instantiationService = accessor.get(IInstantiationService);
    const editorService = accessor.get(IEditorService);
    const win = instantiationService.createInstance(InsertMethodWindow);
    win.onMethodInserted(({ methodName, parameters, testCaseContext }) => {
        let snippet = win.eventHandler.getFormattedParameters() || '';
        const editor = editorService.activeTextEditorControl as any;
        if (editor && editor.executeEdits) {
            const sel = editor.getSelection();
            const model = editor.getModel && editor.getModel();
            let indentPrefix = '';
            let parentBlockLine = -1; // 记录包含空节点的父级块行号

            if (model && model.getLineContent) {
                // 查找最近的 setup/teardown 或 步骤 上下文
                for (let i = sel.startLineNumber; i >= 1; i--) {
                    const line = model.getLineContent(i);
                    const trimmed = line.trim();
                    const baseIndent = (line.match(/^(\s*)/) || ['', ''])[1].length;

                    // 检查 setup/teardown 块
                    if (/^setup\s*:/.test(trimmed) || /^teardown\s*:/.test(trimmed)) {
                        indentPrefix = ' '.repeat(baseIndent + 2);

                        // 检查是否为空节点（包含 ~ 或 null）
                        if (trimmed.includes(': ~') || trimmed.includes(': null') ||
                            trimmed.includes('：~') || trimmed.includes('：null')) {
                            parentBlockLine = i;
                        }
                        break;
                    }

                    // 检查步骤块
                    if (/^\-\s*步骤.*[:：]/.test(trimmed)) {
                        indentPrefix = ' '.repeat(baseIndent + 2);

                        // 检查是否为空节点（包含 ~ 或 null）
                        if (trimmed.includes(': ~') || trimmed.includes(': null') ||
                            trimmed.includes('：~') || trimmed.includes('：null')) {
                            parentBlockLine = i;
                        }
                        break;
                    }
                }
            }

            // 生成插入文本：剥离 snippet 原有缩进，前置上下文 indentPrefix
            const linesArr = snippet.split('\n');
            const indentLens = linesArr.filter(l => l.trim()).map(l => (l.match(/^(\s*)/) || ['', ''])[1].length);
            const minIndent = indentLens.length ? Math.min(...indentLens) : 0;
            const toInsert = linesArr.map(line => indentPrefix + line.slice(minIndent)).join('\n');

            // 如果检测到父级块包含空节点，需要先处理空节点
            if (parentBlockLine !== -1) {
                const parentLine = model.getLineContent(parentBlockLine);

                // 移除父级块中的空值标识符（~ 或 null）
                const cleanedParentLine = parentLine.replace(/:[ \t]*(~|null)[ \t]*$/, ':');

                // 执行两个编辑操作：1. 清理父级块空节点，2. 插入方法
                const edits = [
                    // 清理父级块的空节点标识符
                    {
                        range: {
                            startLineNumber: parentBlockLine,
                            startColumn: 1,
                            endLineNumber: parentBlockLine,
                            endColumn: parentLine.length + 1
                        },
                        text: cleanedParentLine
                    },
                    // 插入方法内容
                    {
                        range: {
                            startLineNumber: sel.startLineNumber,
                            startColumn: 1,
                            endLineNumber: sel.startLineNumber,
                            endColumn: 1
                        },
                        text: toInsert
                    }
                ];

                editor.executeEdits('gat', edits);
            } else {
                // 没有检测到空节点，按原逻辑插入
                const range = {
                    startLineNumber: sel.startLineNumber,
                    startColumn: 1,
                    endLineNumber: sel.startLineNumber,
                    endColumn: 1
                };
                editor.executeEdits('gat', [{ range, text: toInsert }]);
            }
        }
    });

    const editor = editorService.activeTextEditorControl as any;
    const model = editor.getModel && editor.getModel();
    const selection = editor.getSelection();
    if (model && selection) {
        const content = model.getValueInRange(new Range(1, 1, selection.startLineNumber, selection.startColumn));
        win.initGlobalReturnVar(content);
    }
    await win.show();
});

// 在 GAT 子菜单下添加"插入公共方法"菜单项
MenuRegistry.appendMenuItem(GAT_EDITOR_CONTEXT_MENU, {
    command: {
        id: 'gat.insertCommonMethod',
        title: localize('insertCommonMethod', "插入公共方法")
    },
    group: '1_gat',
    order: 1
});

// 新增：注册"修复 key"命令，检测当前行包含 key 字段时触发
CommandsRegistry.registerCommand('gat.fixKey', async (accessor) => {
    const editor = accessor.get(IEditorService).activeTextEditorControl as ICodeEditor;
    const notificationService = accessor.get(INotificationService);
    const pos = editor?.getPosition();
    const model = editor?.getModel();
    if (!editor || !model || !pos) {
        return;
    }
    const lineContent = model.getLineContent(pos.lineNumber);
    // 只要当前行包含 key: 即可触发
    if (!/\bkey\s*:/i.test(lineContent)) {
        notificationService.warn(localize('fixKeyLineNoKey', "当前行不包含 key 字段，无法修复。"));
        return;
    }
    notificationService.info(localize('fixKeyStart', "开始修复 key"));
    // 从当前测试用例行向上查找 driver 字段
    const fullText = model.getValue();
    const allLines = fullText.split(/\r?\n/);
    let driver: string | undefined;
    for (let k = pos.lineNumber - 2; k >= 0; k--) {
        const prevLine = allLines[k];
        const match = /^\s*driver\s*:\s*['"]?([^'"\r\n]+)['"]?/.exec(prevLine);
        if (match) {
            driver = match[1];
            break;
        }
        if (/^-\s*action\s*:/.test(prevLine)) {
            break;
        }
    }
    if (!driver) {
        notificationService.error(localize('fixKeyNoDriver', "无法检测到 driver，无法修复 key。"));
        return;
    }
    const instantiationService = accessor.get(IInstantiationService);
    const win = instantiationService.createInstance(FixKeyWindow);
    await win.show(driver);
});

// 新增：在 GAT 子菜单下添加"修复 key"菜单项，仅当处于 key 字段时可见
MenuRegistry.appendMenuItem(GAT_EDITOR_CONTEXT_MENU, {
    command: {
        id: 'gat.fixKey',
        title: localize('fixKey', "修复 key")
    },
    group: '1_gat',
    order: 2,
    when: ContextKeyExpr.equals('gat:isKeyField', true)
});

// 在GAT视图容器工具栏添加切换到Explorer的按钮
MenuRegistry.appendMenuItem(MenuId.ViewContainerTitle, {
    command: {
        id: 'gat.switchToExplorer',
        title: localize('switchToExplorer', "切换到资源管理器"),
        icon: Codicon.files
    },
    group: 'navigation',
    order: 1,
    when: ContextKeyExpr.equals('viewContainer', GAT_VIEW_CONTAINER_ID)
});

// 在Explorer视图容器工具栏添加切换到GAT的按钮
MenuRegistry.appendMenuItem(MenuId.ViewContainerTitle, {
    command: {
        id: 'gat.switchToGAT',
        title: localize('switchToGAT', "切换到GAT"),
        icon: gatIcon
    },
    group: 'navigation',
    order: 1,
    when: ContextKeyExpr.equals('viewContainer', 'workbench.view.explorer')
});

// 注册命令，让欢迎页按钮触发 GAT 模块命令
CommandsRegistry.registerCommand('gat.openTestSet', async (accessor) => {
    const viewsService = accessor.get(IViewsService);
    await viewsService.openViewContainer(GAT_VIEW_CONTAINER_ID, true);
    const view = viewsService.getActiveViewWithId(TestSetView.ID) as TestSetView;
    if (view) {
        return view.openTestSet();
    }
});
CommandsRegistry.registerCommand('gat.newTestSet', async (accessor) => {
    const viewsService = accessor.get(IViewsService);
    await viewsService.openViewContainer(GAT_VIEW_CONTAINER_ID, true);
    const view = viewsService.getActiveViewWithId(TestSetView.ID) as TestSetView;
    if (view) {
        return view.createNewTestSet();
    }
});

// 确保YAML文件类型关联正确，并在控制台打印相关信息
class FileTypeChecker implements IWorkbenchContribution {
    constructor(
        @ILanguageService private readonly languageService: ILanguageService
    ) {
        console.error(`[DEBUG-HOVER] FileTypeChecker: 正在检查YAML语言关联...`);

        // 检查并打印相关语言ID和扩展名映射
        this.checkYamlLanguageAssociation();
    }

    private checkYamlLanguageAssociation(): void {
        try {
            // 输出.yml和.yaml文件的语言关联
            const ymlLanguageId = this.languageService.guessLanguageIdByFilepathOrFirstLine(URI.parse('file:///test.yml'));
            const yamlLanguageId = this.languageService.guessLanguageIdByFilepathOrFirstLine(URI.parse('file:///test.yaml'));

            console.error(`[DEBUG-HOVER] .yml文件的语言ID: ${ymlLanguageId}`);
            console.error(`[DEBUG-HOVER] .yaml文件的语言ID: ${yamlLanguageId}`);

            if (ymlLanguageId !== 'yaml' || yamlLanguageId !== 'yaml') {
                console.error(`[DEBUG-HOVER] 警告: YAML文件类型映射错误！`);
            } else {
                console.error(`[DEBUG-HOVER] YAML文件类型映射正确`);
            }
        } catch (e) {
            console.error(`[DEBUG-HOVER] 检查YAML语言关联时出错: ${e}`);
        }
    }
}

// 注册FileTypeChecker，确保它在扩展激活早期运行
Registry.as<IWorkbenchContributionsRegistry>(WorkbenchExtensions.Workbench).registerWorkbenchContribution(
    FileTypeChecker,
    LifecyclePhase.Restored
);


//注册一个拖拽监视器，当监听到文本拖拽事件时，执行GAT的插入公共方法命令
class DragMonitor implements IWorkbenchContribution {
    private readonly pathResolver: GATPathResolver;
    constructor(
        @IInstantiationService private readonly instantiationService: IInstantiationService,
        @IEditorService private readonly editorService: IEditorService,
        @IFileService private readonly fileService: IFileService,
        @IConfigurationService private readonly configurationService: IConfigurationService,
        @ILogService private readonly logService: ILogService,
        @IWorkspaceContextService private readonly workspaceContextService: IWorkspaceContextService,

    ) {
        // 初始化路径解析器
        this.pathResolver = new GATPathResolver(
            this.configurationService,
            this.workspaceContextService,
            this.fileService,
            this.logService
        );
        this.registerDragListener();

        this.editorService.onDidActiveEditorChange(() => {
            this.registerDragListener();
        });
    }

    private registerDragListener(): void {
        console.log(`[DragMonitor]: 注册拖拽监听器...`);
        const editor = this.editorService.activeTextEditorControl as ICodeEditor;
        if (!editor) {
            return;
        }
        console.log(`[DragMonitor]:: 已注册拖拽监听器`);
        const getDomNode = editor.getDomNode();
        if (!getDomNode) {
            console.error(`[DragMonitor]:: 无法获取编辑器DOM节点`);
            return;
        }
        getDomNode.addEventListener('drop', (e) => {
            const dataTransfer = e.dataTransfer;
            if (dataTransfer) {
                const text = dataTransfer.getData('text/plain');
                try {
                    const dragContent = JSON.parse(text);
                    const methodName = dragContent.name;
                    const overview = dragContent.overview;
                    console.log(`[DragMonitor]: 解析后的拖拽内容：方法名=${methodName}, 概述=${overview}`);
                    const win = this.instantiationService.createInstance(InsertMethodWindow);
                    const model = editor.getModel();
                    if (model) {
                        const selection = editor.getSelection();
                        if (selection) {
                            const content = model.getValueInRange(new Range(1, 1, selection.startLineNumber, selection.startColumn));
                            win.initGlobalReturnVar(content);
                        }

                        const fileUriStr = model.uri.toString();
                        const getPos = editor.getPosition();
                        if (getPos) {

                            const parameters = this.loadYamlPath(methodName)
                                .then(() => {
                                    //将parameters转成string,传递给InsertMethodWindow
                                    const parametersYAML = JSON.stringify(parameters, null, 2);
                                    win.setEditContext(methodName, parametersYAML, fileUriStr, getPos.lineNumber, getPos.lineNumber);
                                    win.setDragMode(true);
                                    console.log(`[DragMonitor]:方法名=${methodName},
                                             参数概述=${parametersYAML}, 文件=${fileUriStr},
                                             行=${getPos.lineNumber}`);
                                    const editor = this.editorService.activeTextEditorControl as any;
                                    win.show();
                                    //等待窗口关闭后，清除编辑器选中内容
                                    win.onMethodInserted(async ({ methodName, parameters, testCaseContext }) => {
                                        let snippet = win.eventHandler.getFormattedParameters() || '';

                                        if (editor && editor.executeEdits) {
                                            const sel = editor.getSelection();
                                            const model = editor.getModel && editor.getModel();
                                            let indentPrefix = '';
                                            let parentBlockLine = -1; // 记录包含空节点的父级块行号

                                            if (model && model.getLineContent) {
                                                // 查找最近的 setup/teardown 或 步骤 上下文
                                                for (let i = sel.startLineNumber; i >= 1; i--) {
                                                    const line = model.getLineContent(i);
                                                    const trimmed = line.trim();
                                                    const baseIndent = (line.match(/^(\s*)/) || ['', ''])[1].length;

                                                    // 检查 setup/teardown 块
                                                    if (/^setup\s*:/.test(trimmed) || /^teardown\s*:/.test(trimmed)) {
                                                        indentPrefix = ' '.repeat(baseIndent + 2);

                                                        // 检查是否为空节点（包含 ~ 或 null）
                                                        if (trimmed.includes(': ~') || trimmed.includes(': null') ||
                                                            trimmed.includes('：~') || trimmed.includes('：null')) {
                                                            parentBlockLine = i;
                                                        }
                                                        break;
                                                    }

                                                    // 检查步骤块
                                                    if (/^\-\s*步骤.*[:：]/.test(trimmed)) {
                                                        indentPrefix = ' '.repeat(baseIndent + 2);

                                                        // 检查是否为空节点（包含 ~ 或 null）
                                                        if (trimmed.includes(': ~') || trimmed.includes(': null') ||
                                                            trimmed.includes('：~') || trimmed.includes('：null')) {
                                                            parentBlockLine = i;
                                                        }
                                                        break;
                                                    }
                                                }
                                            }

                                            // 生成插入文本：剥离 snippet 原有缩进，前置上下文 indentPrefix
                                            const linesArr = snippet.split('\n');
                                            const indentLens = linesArr.filter(l => l.trim()).map(l => (l.match(/^(\s*)/) || ['', ''])[1].length);
                                            const minIndent = indentLens.length ? Math.min(...indentLens) : 0;
                                            const toInsert = linesArr.map(line => indentPrefix + line.slice(minIndent)).join('\n');

                                            // 如果检测到父级块包含空节点，需要先处理空节点
                                            if (parentBlockLine !== -1) {
                                                const parentLine = model.getLineContent(parentBlockLine);

                                                // 移除父级块中的空值标识符（~ 或 null）
                                                const cleanedParentLine = parentLine.replace(/:[ \t]*(~|null)[ \t]*$/, ':');

                                                // 执行两个编辑操作：1. 清理父级块空节点，2. 插入方法
                                                const edits = [
                                                    // 清理父级块的空节点标识符
                                                    {
                                                        range: {
                                                            startLineNumber: parentBlockLine,
                                                            startColumn: 1,
                                                            endLineNumber: parentBlockLine,
                                                            endColumn: parentLine.length + 1
                                                        },
                                                        text: cleanedParentLine
                                                    },
                                                    // 插入方法内容
                                                    {
                                                        range: {
                                                            startLineNumber: sel.startLineNumber,
                                                            startColumn: 1,
                                                            endLineNumber: sel.startLineNumber,
                                                            endColumn: 1
                                                        },
                                                        text: toInsert
                                                    }
                                                ];

                                                editor.executeEdits('gat', edits);
                                            } else {
                                                // 没有检测到空节点，按原逻辑插入
                                                const range = {
                                                    startLineNumber: sel.startLineNumber,
                                                    startColumn: 1,
                                                    endLineNumber: sel.startLineNumber,
                                                    endColumn: 1
                                                };
                                                editor.executeEdits('gat', [{ range, text: toInsert }]);
                                            }

                                        }
                                    });
                                    //如果不是在当前光标位置拖拽的，释放完成后清除当前光标位置所在行的内容
                                    const getCurPos = editor.getPosition();
                                    if (getCurPos) {
                                        const range = {
                                            startLineNumber: getCurPos.lineNumber,
                                            startColumn: 1,
                                            endLineNumber: getCurPos.lineNumber,
                                            endColumn: model.getLineMaxColumn(getCurPos.lineNumber)
                                        };
                                        editor.executeEdits('gat', [{ range, text: '' }]);
                                    }

                                });
                        }
                    }
                } catch (error) {
                    console.error(`[DragMonitor]: 无法解析拖拽内容=${text}`, error);
                }
            }
        });
        getDomNode.addEventListener('dragover', (e) => {
            e.preventDefault(); // 允许拖放事件
        });
    }

    //解析yaml公共方法文件的路径
    private async loadYamlPath(method: string): Promise<any> {
        try {
            // 使用路径解析器获取 action_keywords 目录
            const actionKeywordsPath = await this.pathResolver.getActionKeywordsPath();
            if (!actionKeywordsPath) {
                this.logService.error('[DragMonitor]: 无法获取有效的 action_keywords 路径');
                return;
            }
            // 获取 action_yaml 子目录
            const actionYamlPath = joinPath(actionKeywordsPath, 'action_yaml');
            try {
                await this.fileService.resolve(actionYamlPath);
                this.logService.info(`找到 action_yaml 目录: ${actionYamlPath.toString()}`);
                // 获取文件系统条目
                const dirContent = await this.fileService.resolve(actionYamlPath);
                if (!dirContent.children) {
                    this.logService.error('无法读取action_yaml目录内容');
                    return;
                }

                // 过滤出YAML文件
                const yamlFileEntries = dirContent.children.filter(entry =>
                    entry.name.endsWith('.yml') || entry.name.endsWith('.yaml')
                );

                // 遍历YAML文件
                for (const entry of yamlFileEntries) {
                    const fileUri = entry.resource;
                    const fileName = basename(fileUri.path);

                    try {
                        // 读取文件内容
                        const content = await this.fileService.readFile(fileUri);
                        const yamlContent = content.value.toString();
                        //简单解析yamlContent，一行一行读取
                        const lines = yamlContent.split('\n');
                        for (let line of lines) {
                            // 跳过注释和空行
                            line = line.trim();
                            if (line.startsWith('#') || line === '') {
                                continue;
                            }
                            // 匹配method
                            if (line.startsWith(method) && line.endsWith(':')) {
                                console.log(`[DragMonitor]: 找到公共方法 ${method} 定义在 ${fileName}`);
                                //获取后面的参数，直到遇到下一个kylinronbot_开头的行
                                let parameters = '';
                                let isEnd = false;
                                for (let i = lines.indexOf(line) + 1; i < lines.length; i++) {
                                    const nextLine = lines[i].trim();
                                    if (nextLine.startsWith('kyrobot_')) {
                                        isEnd = true;
                                        break;
                                    }
                                    parameters += nextLine + '\n';
                                }
                                if (isEnd) {
                                    console.log(`[DragMonitor]: 公共方法 ${method} 定义在 ${fileName} 中，参数为 ${parameters}`);
                                    return parameters;
                                }
                            }
                        }

                    } catch (error) {
                        this.logService.error(`读取YAML文件 ${fileName} 失败:`, error);
                    }
                }

            } catch (error) {
                this.logService.error('action_yaml 子目录不存在,未找到公共方法定义目录，请检查配置');
                return;
            }

        } catch (error) {
            this.logService.error('[DragMonitor]: 获取 action_keywords 路径失败:', error);
        }

    }

    // 释放资源
    dispose(): void {
        console.log(`[DragMonitor]: 已释放资源`);
    }
}

Registry.as<IWorkbenchContributionsRegistry>(WorkbenchExtensions.Workbench).registerWorkbenchContribution(
    DragMonitor,
    LifecyclePhase.Restored // 注册时机：扩展激活时
);

// 注册一个登录监听，根据登录状态显示或者隐藏GAT视图
class GATAuthMonitor implements IWorkbenchContribution {
    constructor(
        @IViewDescriptorService private readonly viewDescriptorService: IViewDescriptorService,
        @IPaneCompositePartService private readonly paneCompositeService: IPaneCompositePartService,
        @ICustomAuthenticationService private readonly customAuthService: ICustomAuthenticationService,
        @ILogService private readonly logService: ILogService

    ) {
        this.registerHandler();
        this.changeGatShowStatus();
    }

    private registerHandler(): void {
        this.customAuthService.onDidAuthenticationChangedProvider(() => {
            this.changeGatShowStatus();
        });
    }

    private async changeGatShowStatus(): Promise<void> {
        if (this.customAuthService.isLoggedIn()) {
            this.showGATViewContainer();
        } else {
            this.hideGATViewContainer();
        }
    }

    async showGATViewContainer(): Promise<void> {
        this.logService.info('[showGATViewContainer] 切换到GAT视图');
        this.viewDescriptorService.moveViewContainerToLocation(GAT_VIEW_CONTAINER, ViewContainerLocation.Sidebar);
        await this.paneCompositeService.openPaneComposite('workbench.view.gat', ViewContainerLocation.Sidebar, true);
        this.logService.info('[showGATViewContainer] GAT视图切换完毕');
    }

    private hideGATViewContainer(): void {
        this.logService.info('[hideGATViewContainer] 隐藏GAT视图');
        this.viewDescriptorService.moveViewContainerToLocation(GAT_VIEW_CONTAINER, ViewContainerLocation.Panel);
        this.logService.info('[hideGATViewContainer] GAT视图隐藏完毕');
    }
}

Registry.as<IWorkbenchContributionsRegistry>(WorkbenchExtensions.Workbench).registerWorkbenchContribution(
    GATAuthMonitor,
    LifecyclePhase.Eventually
);

