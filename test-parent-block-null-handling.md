# 右键插入时父级块空节点处理功能

## 功能改进

在右键插入公共方法时，增加了对父级块空节点的自动检测和处理功能。现在可以同时处理YAML中的两种空值表示：
- `~` (波浪号)
- `null`

**📝 缩进修复**：修正了步骤块中action节点的缩进逻辑，从 `baseIndent + 4` 改为 `baseIndent + 2`，确保符合正确的YAML缩进规范。

## 修改的文件

`src/vs/workbench/contrib/gat/browser/gat.contribution.ts` - 在 `gat.insertCommonMethod` 命令中增加了空节点处理逻辑

## 功能详情

### 检测范围

系统会检测以下类型的父级块：
1. **setup块**: `setup: ~` 或 `setup: null`
2. **teardown块**: `teardown: ~` 或 `teardown: null`
3. **步骤块**: `- 步骤1：测试步骤: ~` 或 `- 步骤1：测试步骤: null`

### 处理逻辑

当在包含空节点的父级块内部右键插入公共方法时：

1. **检测阶段**：
   - 向上查找最近的父级块（setup/teardown/步骤）
   - 检查该块是否包含空值标识符（~ 或 null）

2. **处理阶段**：
   - 如果检测到空节点：自动移除空值标识符，然后插入方法内容
   - 如果没有空节点：按原逻辑直接插入方法内容

## 测试场景

### 场景1：setup块包含空节点
```yaml
# 插入前
setup: ~
teardown:
  - action: close_app

# 在setup下方右键插入方法后
setup:
  - action: open_app
    kwargs:
      app_name: "测试应用"
teardown:
  - action: close_app
```

### 场景2：步骤块包含null空节点
```yaml
# 插入前
steps:
  - 步骤1：打开应用: null
  - 步骤2：执行操作:
    - action: click_button

# 在步骤1下方右键插入方法后
steps:
  - 步骤1：打开应用:
    - action: open_app
      kwargs:
        app_name: "测试应用"
  - 步骤2：执行操作:
    - action: click_button
```

### 场景3：teardown块包含混合空节点
```yaml
# 插入前
setup:
  - action: open_app
teardown: ~

# 在teardown下方右键插入方法后
setup:
  - action: open_app
teardown:
  - action: close_app
    kwargs:
      force: true
```

## 技术实现

### 空节点检测
```typescript
// 检查是否为空节点（包含 ~ 或 null）
if (trimmed.includes(': ~') || trimmed.includes(': null') ||
    trimmed.includes('：~') || trimmed.includes('：null')) {
    parentBlockLine = i;  // 记录父级块行号
}
```

### 空值标识符清理
```typescript
// 移除父级块中的空值标识符（~ 或 null）
const cleanedParentLine = parentLine.replace(/:[ \t]*(~|null)[ \t]*$/, ':');
```

### 双重编辑操作
```typescript
const edits = [
    // 1. 清理父级块的空节点标识符
    {
        range: { /* 父级块行范围 */ },
        text: cleanedParentLine
    },
    // 2. 插入方法内容
    {
        range: { /* 插入位置范围 */ },
        text: toInsert
    }
];
```

## 优势

1. **自动化处理**：无需手动删除空节点标识符
2. **兼容性强**：同时支持 `~` 和 `null` 两种空值表示
3. **上下文感知**：智能识别不同类型的父级块
4. **原子操作**：确保空节点清理和方法插入同时完成
5. **向后兼容**：不影响非空节点的正常插入流程

## 使用方法

1. 在包含空节点的父级块内部右键
2. 选择 "Robot" -> "插入公共方法"
3. 在弹出的窗口中选择要插入的方法
4. 系统会自动清理空节点并插入方法内容
