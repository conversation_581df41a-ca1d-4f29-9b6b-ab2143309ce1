#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试菜单监听器功能

作者: GAT开发团队
日期: 2025年1月4日
"""

import os
import sys
import subprocess
import time
import threading

def test_menu_listener_startup():
    """测试菜单监听器启动"""
    print("🔍 测试菜单监听器启动")
    print("="*50)
    
    print("📋 测试场景:")
    print("1. scripts目录执行 - 应该能启动菜单监听器")
    print("2. scripts/recorder目录执行 - 应该显示脚本不存在")
    
    scenarios = [
        {
            'name': 'scripts目录执行',
            'cwd': '/home/<USER>/kylin-robot-ide/scripts',
            'cmd': ['python3', 'recorder/run_recorder.py', '--debug', '--duration', '3'],
            'expect_menu_listener': True
        },
        {
            'name': 'scripts/recorder目录执行',
            'cwd': '/home/<USER>/kylin-robot-ide/scripts/recorder',
            'cmd': ['python3', 'run_recorder.py', '--debug', '--duration', '3'],
            'expect_menu_listener': False
        }
    ]
    
    for scenario in scenarios:
        print(f"\n🔵 {scenario['name']}")
        print(f"   工作目录: {scenario['cwd']}")
        print(f"   命令: {' '.join(scenario['cmd'])}")
        print(f"   预期菜单监听器: {'启动' if scenario['expect_menu_listener'] else '不启动'}")
        
        if not os.path.exists(scenario['cwd']):
            print(f"   ❌ 目录不存在")
            continue
        
        try:
            start_time = time.time()
            
            # 启动进程
            process = subprocess.Popen(
                scenario['cmd'],
                cwd=scenario['cwd'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            print(f"   🚀 进程已启动，PID: {process.pid}")
            
            # 监控3秒
            monitor_duration = 3
            menu_listener_started = False
            menu_listener_failed = False
            menu_script_not_found = False
            
            while time.time() - start_time < monitor_duration:
                if process.poll() is not None:
                    print(f"   ✅ 进程已结束")
                    break
                
                # 读取输出
                try:
                    import select
                    ready, _, _ = select.select([process.stdout, process.stderr], [], [], 0.1)
                    
                    if process.stderr in ready:
                        line = process.stderr.readline()
                        if line:
                            line = line.strip()
                            
                            # 检测关键信息
                            if '菜单监听器已启动' in line:
                                menu_listener_started = True
                                print(f"   ✅ 菜单监听器启动成功")
                            elif '菜单监听器启动失败' in line:
                                menu_listener_failed = True
                                print(f"   ❌ 菜单监听器启动失败")
                            elif '菜单监听脚本不存在' in line:
                                menu_script_not_found = True
                                print(f"   ℹ️  菜单监听脚本不存在")
                            elif '菜单监听进程启动成功' in line:
                                print(f"   📋 菜单监听进程启动成功")
                
                except Exception:
                    break
                
                time.sleep(0.1)
            
            # 终止进程
            if process.poll() is None:
                print(f"   ⏰ 监控结束，终止进程...")
                process.terminate()
                try:
                    process.wait(timeout=2)
                except subprocess.TimeoutExpired:
                    process.kill()
                    process.wait()
            
            # 评估结果
            if scenario['expect_menu_listener']:
                if menu_listener_started:
                    print(f"   🎉 测试通过：菜单监听器正常启动")
                elif menu_script_not_found:
                    print(f"   ⚠️  脚本路径问题：找不到listenHF.py")
                elif menu_listener_failed:
                    print(f"   ❌ 测试失败：菜单监听器启动失败")
                else:
                    print(f"   ❓ 未检测到菜单监听器状态")
            else:
                if menu_script_not_found:
                    print(f"   ✅ 测试通过：符合预期（脚本不存在）")
                elif menu_listener_started:
                    print(f"   ⚠️  意外结果：菜单监听器启动了")
                else:
                    print(f"   ✅ 测试通过：菜单监听器未启动")
            
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")

def test_menu_file_creation():
    """测试菜单文件创建"""
    print(f"\n🔍 测试菜单文件创建")
    print("="*40)
    
    menu_file = "/tmp/.recordmenu.txt"
    
    print(f"📋 菜单记录文件: {menu_file}")
    
    # 检查文件是否存在
    if os.path.exists(menu_file):
        print(f"   📄 文件已存在")
        
        # 检查文件内容
        try:
            with open(menu_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if content.strip():
                print(f"   📝 文件有内容 ({len(content)}字符)")
                print(f"   内容预览: {content[:100]}...")
            else:
                print(f"   📝 文件为空（正常，无菜单事件）")
        
        except Exception as e:
            print(f"   ❌ 读取文件失败: {e}")
    
    else:
        print(f"   📄 文件不存在（正常，菜单监听器可能未启动或无事件）")

def test_menu_process_status():
    """测试菜单监听进程状态"""
    print(f"\n🔍 测试菜单监听进程状态")
    print("="*40)
    
    try:
        # 查找listenHF.py进程
        result = subprocess.run(
            ['pgrep', '-f', 'listenHF.py'],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            pids = result.stdout.strip().split('\n')
            print(f"   📋 找到 {len(pids)} 个listenHF.py进程:")
            
            for pid in pids:
                if pid.strip():
                    print(f"      PID: {pid}")
                    
                    # 获取进程详细信息
                    try:
                        ps_result = subprocess.run(
                            ['ps', '-p', pid, '-o', 'pid,ppid,cmd'],
                            capture_output=True,
                            text=True
                        )
                        
                        if ps_result.returncode == 0:
                            lines = ps_result.stdout.strip().split('\n')
                            if len(lines) > 1:
                                print(f"      详情: {lines[1]}")
                    
                    except Exception:
                        pass
        
        else:
            print(f"   📋 没有找到listenHF.py进程")
    
    except Exception as e:
        print(f"   ❌ 检查进程失败: {e}")

def main():
    print("🔍 GAT菜单监听器功能测试")
    print("="*50)
    
    print("🎯 测试目标:")
    print("• 验证菜单监听器能正常启动")
    print("• 检查菜单记录文件创建")
    print("• 确认菜单监听进程运行状态")
    print("• 验证协调机制不影响菜单功能")
    
    # 测试菜单监听器启动
    test_menu_listener_startup()
    
    # 测试菜单文件创建
    test_menu_file_creation()
    
    # 测试菜单进程状态
    test_menu_process_status()
    
    print(f"\n🎉 菜单监听器功能测试完成！")
    print("如果菜单监听器正常启动，说明功能已恢复")

if __name__ == "__main__":
    main()
