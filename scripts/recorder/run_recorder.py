#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""自动录制管理器主程序 - 确保控件分析器正确初始化"""
import os
import sys
import time
import threading
import queue
import uuid
import json
import math
import traceback
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import signal
import importlib.util

# 添加项目根目录到Python路径，确保可以导入recorder包
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from recorder.utils.locator_generator import LocatorGenerator
from recorder.utils.logger import enable_timestamped_print
from recorder.utils.argument_utils import args
from recorder.utils.imports import PYNPUT_AVAILABLE, pynput_mouse, pynput_keyboard, UNI_AVAILABLE, \
	UNI, LOCATOR_GENERATOR_AVAILABLE
from recorder.models.session import RecordingSession
from recorder.models.events import MouseEvent, KeyboardEvent
from recorder.detectors.hover_detector import HoverDetector
from recorder.detectors.drag_detector import DragDetector
from recorder.detectors.double_click_detector import DoubleClickDetector
from recorder.utils.command_handler import CommandHandler
from recorder.utils import save_data_json
from recorder.utils.locator_utils import LocatorUtils

import traceback
from Xlib import display  # 需确保Xlib库可用
from dataclasses import asdict
from pathlib import Path

base_dir = Path(__file__).parent.parent


def _clean_data_for_json(data):
	"""清理数据中的特殊字符，避免JSON序列化问题"""
	if isinstance(data, dict):
		return {k: _clean_data_for_json(v) for k, v in data.items()}
	elif isinstance(data, list):
		return [_clean_data_for_json(item) for item in data]
	elif isinstance(data, str):
		# 移除或替换可能导致JSON问题的特殊字符
		return data.replace('\x00', '').replace('\r', '\\r').replace('\n', '\\n')
	else:
		return data


def output_json_event(event_type: str, data: Dict[str, Any]):
	"""输出JSON格式的事件到stdout，供VSCode接收 - 与auto_recording_manager.py相同的实现"""
	try:
		# 清理数据中的特殊字符，避免JSON序列化问题
		cleaned_data = _clean_data_for_json(data)

		event = {
			'type': event_type,
			'timestamp': time.time(),
			'data': cleaned_data
		}

		# 使用更安全的JSON序列化
		json_str = json.dumps(event, ensure_ascii=False, separators=(',', ':'))
		print(json_str)
		sys.stdout.flush()

	except Exception as e:
		print(f"[ERROR] 输出JSON事件失败: {e}", file=sys.stderr)
		# 尝试输出一个简化的错误事件
		try:
			error_event = {
				'type': 'error',
				'timestamp': time.time(),
				'data': {'error': f'JSON序列化失败: {str(e)}', 'original_type': event_type}
			}
			print(json.dumps(error_event, ensure_ascii=True))
			sys.stdout.flush()
		except:
			pass  # 如果连错误事件都无法输出，就放弃


class MenuListenerManager:
	"""
	菜单监听管理器
	负责启动和管理listenHF.py进程，用于监听弹出菜单事件
	"""

	def __init__(self, debug: bool = False):
		self.debug = debug
		self.listener_process = None
		self.running = False
		self.menu_file = "/tmp/.recordmenu.txt"

		# 协调机制：暂停/恢复菜单监听
		self.pause_file = "/tmp/.pause_menu_listener.txt"
		self.coordination_enabled = True

		# 获取listenHF.py的路径 - 使用绝对路径避免工作目录问题
		current_file = Path(__file__).resolve()  # 获取当前文件的绝对路径
		recorder_dir = current_file.parent  # scripts/recorder
		scripts_dir = recorder_dir.parent  # scripts
		self.listener_script = scripts_dir / "listenHF.py"

		if not self.listener_script.exists():
			print(f"[ERROR] 菜单监听脚本不存在: {self.listener_script}", file=sys.stderr)

	def start_listener(self) -> bool:
		"""启动菜单监听进程"""
		if self.running:
			if self.debug:
				print("[DEBUG] 菜单监听器已经在运行", file=sys.stderr)
			return True

		if not self.listener_script.exists():
			print(f"[ERROR] 菜单监听脚本不存在: {self.listener_script}", file=sys.stderr)
			return False

		try:
			if self.debug:
				print(f"[DEBUG] 启动菜单监听进程: {self.listener_script}", file=sys.stderr)

			# 不清空菜单记录文件，保留之前的菜单内容
			# 菜单监听器会根据需要管理文件内容
			if self.debug:
				print(f"[DEBUG] 菜单记录文件路径: {self.menu_file}", file=sys.stderr)

			# 准备环境变量，确保DISPLAY正确设置
			env = os.environ.copy()
			if 'DISPLAY' not in env or not env['DISPLAY']:
				env['DISPLAY'] = ':0'  # 默认使用 :0
				if self.debug:
					print(f"[DEBUG] 设置DISPLAY环境变量为: {env['DISPLAY']}", file=sys.stderr)

			# 启动监听进程
			self.listener_process = subprocess.Popen(
				[sys.executable, str(self.listener_script)],
				stdout=subprocess.PIPE,
				stderr=subprocess.PIPE,
				env=env,  # 传递修正后的环境变量
				cwd=str(self.listener_script.parent.parent),  # 设置工作目录为项目根目录
				preexec_fn=os.setsid  # 创建新的进程组
			)

			# 等待一小段时间让进程启动
			time.sleep(0.5)

			# 检查进程是否正常启动
			if self.listener_process.poll() is None:
				self.running = True
				if self.debug:
					print(f"[DEBUG] ✅ 菜单监听进程启动成功，PID: {self.listener_process.pid}", file=sys.stderr)
				return True
			else:
				# 进程启动失败，获取错误信息
				stdout, stderr = self.listener_process.communicate()
				print(f"[ERROR] 菜单监听进程启动失败:", file=sys.stderr)
				if stdout:
					print(f"stdout: {stdout.decode()}", file=sys.stderr)
				if stderr:
					print(f"stderr: {stderr.decode()}", file=sys.stderr)
				self.listener_process = None
				return False

		except Exception as e:
			print(f"[ERROR] 启动菜单监听进程时发生错误: {e}", file=sys.stderr)
			self.listener_process = None
			return False

	def pause_listener(self):
		"""暂停菜单监听器（用于悬停检测期间）"""
		if self.coordination_enabled:
			try:
				with open(self.pause_file, 'w') as f:
					f.write("paused")
				if self.debug:
					print(f"[DEBUG] 菜单监听器已暂停", file=sys.stderr)
			except Exception as e:
				if self.debug:
					print(f"[DEBUG] 暂停菜单监听器失败: {e}", file=sys.stderr)

	def resume_listener(self):
		"""恢复菜单监听器"""
		if self.coordination_enabled:
			try:
				if os.path.exists(self.pause_file):
					os.remove(self.pause_file)
				if self.debug:
					print(f"[DEBUG] 菜单监听器已恢复", file=sys.stderr)
			except Exception as e:
				if self.debug:
					print(f"[DEBUG] 恢复菜单监听器失败: {e}", file=sys.stderr)

	def stop_listener(self):
		"""停止菜单监听进程"""
		# 清理暂停文件
		self.resume_listener()
		if not self.running or not self.listener_process:
			if self.debug:
				print("[DEBUG] 菜单监听器未运行或进程不存在，跳过停止操作", file=sys.stderr)
			return

		process_pid = self.listener_process.pid
		try:
			print(f"[INFO] 正在停止菜单监听进程，PID: {process_pid}", file=sys.stderr)

			# 检查进程是否还存在
			if self.listener_process.poll() is not None:
				print(f"[INFO] 菜单监听进程 {process_pid} 已经结束", file=sys.stderr)
				return

			# 尝试获取进程组ID
			try:
				pgid = os.getpgid(process_pid)
			except OSError as e:
				print(f"[WARNING] 无法获取进程组ID: {e}，尝试直接终止进程", file=sys.stderr)
				# 如果无法获取进程组，直接终止主进程
				self.listener_process.terminate()
				try:
					self.listener_process.wait(timeout=5)
					print(f"[INFO] ✅ 菜单监听进程 {process_pid} 已通过terminate()结束", file=sys.stderr)
					return
				except subprocess.TimeoutExpired:
					print(f"[WARNING] 进程 {process_pid} terminate()超时，尝试kill()", file=sys.stderr)
					self.listener_process.kill()
					self.listener_process.wait()
					print(f"[INFO] ✅ 菜单监听进程 {process_pid} 已通过kill()结束", file=sys.stderr)
					return

			# 发送SIGTERM信号给整个进程组
			print(f"[DEBUG] 发送SIGTERM信号给进程组 {pgid}", file=sys.stderr)
			os.killpg(pgid, signal.SIGTERM)

			# 等待进程结束
			try:
				self.listener_process.wait(timeout=5)
				print(f"[INFO] ✅ 菜单监听进程 {process_pid} 已正常结束", file=sys.stderr)
			except subprocess.TimeoutExpired:
				print(f"[WARNING] 菜单监听进程 {process_pid} 未响应SIGTERM，强制终止", file=sys.stderr)
				# 强制终止
				try:
					os.killpg(pgid, signal.SIGKILL)
					self.listener_process.wait(timeout=3)
					print(f"[INFO] ✅ 菜单监听进程 {process_pid} 已强制结束", file=sys.stderr)
				except Exception as kill_error:
					print(f"[ERROR] 强制终止进程组失败: {kill_error}", file=sys.stderr)
					# 最后尝试直接kill主进程
					try:
						self.listener_process.kill()
						self.listener_process.wait()
						print(f"[INFO] ✅ 菜单监听进程 {process_pid} 已通过直接kill结束", file=sys.stderr)
					except Exception as final_error:
						print(f"[ERROR] 最终终止进程失败: {final_error}", file=sys.stderr)

		except Exception as e:
			print(f"[ERROR] 停止菜单监听进程时发生错误: {e}", file=sys.stderr)
			print(f"[ERROR] 错误详情: {traceback.format_exc()}", file=sys.stderr)
			# 尝试最后的清理操作
			try:
				if self.listener_process:
					self.listener_process.kill()
					self.listener_process.wait()
					print(f"[INFO] ✅ 菜单监听进程 {process_pid} 已在异常处理中结束", file=sys.stderr)
			except:
				print(f"[ERROR] 最终清理操作也失败", file=sys.stderr)
		finally:
			self.listener_process = None
			self.running = False
			print(f"[INFO] 菜单监听器状态已重置", file=sys.stderr)

	def is_running(self) -> bool:
		"""检查菜单监听器是否正在运行"""
		if not self.listener_process:
			return False

		# 检查进程是否还活着
		if self.listener_process.poll() is not None:
			self.running = False
			return False

		return self.running

	def _force_kill_listenHF_process(self):
		"""强制终止所有listenHF.py进程"""
		try:
			print("[INFO] 正在强制终止所有listenHF.py进程...", file=sys.stderr)

			# 使用pkill命令终止所有listenHF.py进程
			result = subprocess.run(
				['pkill', '-f', 'listenHF.py'],
				capture_output=True,
				text=True,
				timeout=10
			)

			if result.returncode == 0:
				print("[INFO] ✅ 成功终止listenHF.py进程", file=sys.stderr)
			elif result.returncode == 1:
				print("[INFO] 没有找到运行中的listenHF.py进程", file=sys.stderr)
			else:
				print(f"[WARNING] pkill命令返回码: {result.returncode}", file=sys.stderr)
				if result.stderr:
					print(f"[WARNING] pkill错误输出: {result.stderr}", file=sys.stderr)

		except subprocess.TimeoutExpired:
			print("[ERROR] pkill命令超时", file=sys.stderr)
		except FileNotFoundError:
			print("[WARNING] pkill命令不可用，尝试使用ps+kill", file=sys.stderr)
			try:
				# 备用方法：使用ps查找进程然后kill
				ps_result = subprocess.run(
					['ps', 'aux'],
					capture_output=True,
					text=True,
					timeout=10
				)

				if ps_result.returncode == 0:
					lines = ps_result.stdout.split('\n')
					pids_to_kill = []

					for line in lines:
						if 'listenHF.py' in line and 'python' in line:
							parts = line.split()
							if len(parts) >= 2:
								try:
									pid = int(parts[1])
									pids_to_kill.append(pid)
								except ValueError:
									continue

					if pids_to_kill:
						print(f"[INFO] 找到listenHF.py进程PIDs: {pids_to_kill}", file=sys.stderr)
						for pid in pids_to_kill:
							try:
								os.kill(pid, signal.SIGTERM)
								print(f"[INFO] 已发送SIGTERM给进程 {pid}", file=sys.stderr)
							except OSError as e:
								print(f"[WARNING] 无法终止进程 {pid}: {e}", file=sys.stderr)
					else:
						print("[INFO] 没有找到运行中的listenHF.py进程", file=sys.stderr)

			except Exception as backup_error:
				print(f"[ERROR] 备用终止方法失败: {backup_error}", file=sys.stderr)
		except Exception as e:
			print(f"[ERROR] 强制终止listenHF.py进程时发生错误: {e}", file=sys.stderr)


class WidgetAnalyzer:
	"""控件分析器 - 用于识别鼠标位置的控件信息（优化增强版）"""

	def __init__(self, debug=False):
		self.debug = debug
		self.uni = None
		self.cache = {}
		self.cache_timeout = 10  # 缓存超时时间（秒）
		self._initialize_uni()

	def _initialize_uni(self):
		"""初始化UNI模块，添加详细调试信息"""
		if not UNI_AVAILABLE or UNI is None:
			print("[WARNING] UNI模块不可用，控件识别功能将受限", file=sys.stderr)
			return

		try:
			self.uni = UNI()
			if self.debug:
				print("[DEBUG] UNI模块初始化成功，控件分析器就绪", file=sys.stderr)
			else:
				print("[INFO] UNI模块初始化成功", file=sys.stderr)
		except Exception as e:
			print(f"[ERROR] UNI模块初始化失败: {e}", file=sys.stderr)
			if self.debug:
				traceback.print_exc(file=sys.stderr)
			self.uni = None

	def _clear_cache(self):
		"""清理过期缓存，添加调试信息"""
		current_time = time.time()
		# 只保留未过期的缓存项
		self.cache = {k: v for k, v in self.cache.items()
					  if current_time - v['timestamp'] < self.cache_timeout}
		if self.debug:
			print(f"[DEBUG] 清理控件缓存，剩余 {len(self.cache)} 项", file=sys.stderr)

	def force_refresh_atspi_registry(self, interrupt_flag: threading.Event = None) -> bool:
		"""
		强制刷新AT-SPI注册表，确保新打开的应用能被检测到
		增强版刷新功能，解决新应用控件识别问题
		"""
		if not self.uni:
			return False

		try:
			if self.debug:
				print("[DEBUG] 🔄 开始强制刷新AT-SPI注册表...", file=sys.stderr)

			# 导入pyatspi
			import pyatspi

			# 1. 清除相关缓存
			if hasattr(self.uni, 'desktop_cache') and isinstance(self.uni.desktop_cache, dict):
				self.uni.desktop_cache.clear()
				if self.debug:
					print("[DEBUG] 已清除桌面缓存", file=sys.stderr)

			if hasattr(self.uni, 'window_cache'):
				self.uni.window_cache.clear()
				if self.debug:
					print("[DEBUG] 已清除窗口缓存", file=sys.stderr)

			# 2. 重置桌面刷新时间戳
			if hasattr(self.uni, '_last_desktop_refresh'):
				self.uni._last_desktop_refresh = 0

			# 3. 多次获取桌面对象，强制AT-SPI重新扫描
			desktop_attempts = []
			for i in range(3):
				try:
					desktop = pyatspi.Registry.getDesktop(0)
					app_count = desktop.childCount if desktop else 0
					desktop_attempts.append(app_count)

					if self.debug:
						print(f"[DEBUG] 第{i + 1}次获取桌面: {app_count}个应用", file=sys.stderr)

					# 可中断的短暂等待
					for _ in range(5):
						if interrupt_flag and interrupt_flag.is_set():
							if self.debug:
								print(f"[DEBUG] AT-SPI刷新被中断（等待期间）", file=sys.stderr)
							return False
						time.sleep(0.01)
				except Exception as e:
					if self.debug:
						print(f"[DEBUG] 第{i + 1}次获取桌面失败: {e}", file=sys.stderr)

			# 4. 可中断的最终等待
			for _ in range(10):
				if interrupt_flag and interrupt_flag.is_set():
					if self.debug:
						print(f"[DEBUG] AT-SPI刷新被中断（最终等待期间）", file=sys.stderr)
					return False
				time.sleep(0.01)

			if self.debug:
				print("[DEBUG] ✅ AT-SPI注册表刷新完成", file=sys.stderr)
			return True

		except Exception as e:
			print(f"[ERROR] 强制刷新AT-SPI注册表失败: {e}", file=sys.stderr)
			if self.debug:
				traceback.print_exc(file=sys.stderr)
			return False

	def _is_screen_edge_area(self, x: int, y: int) -> bool:
		"""检查是否在屏幕边缘区域，优化面板区域识别"""
		try:
			import subprocess
			result = subprocess.run(['xdpyinfo'], capture_output=True, text=True, timeout=1)
			if result.returncode == 0:
				for line in result.stdout.split('\n'):
					if 'dimensions:' in line:
						dimensions = line.split('dimensions:')[1].split()[0]
						width, height = map(int, dimensions.split('x'))

						edge_threshold = 5  # 边缘阈值（5像素内）
						panel_height = 50  # 面板通常高度

						# 检查是否在面板区域（顶部或底部）
						is_in_panel_area = y >= height - panel_height or y <= panel_height
						if is_in_panel_area:
							if self.debug:
								print(f"[DEBUG] 🎯 检测到面板区域控件: ({x}, {y})，允许识别", file=sys.stderr)
							return False

						# 检查是否在真正的边缘区域
						if (x < edge_threshold or y < edge_threshold or
							x > width - edge_threshold or y > height - edge_threshold):
							return True
						break
		except Exception:
			pass
		return False

	def _is_problematic_window_at_point(self, x: int, y: int) -> bool:
		"""检查指定坐标的窗口是否可能导致卡顿"""
		try:
			if not hasattr(self.uni, 'display_server') or self.uni.display_server != 'x11':
				return False

			disp = display.Display()
			root = disp.screen().root

			# 获取指定坐标的窗口
			window = root.query_pointer().child
			if window:
				# 递归查找最顶层窗口
				while True:
					result = window.query_pointer()
					if result.child:
						window = result.child
					else:
						break

				# 检查窗口类名
				try:
					wm_class = window.get_wm_class()
					if wm_class:
						class_name = wm_class[0] or "Unknown"
						instance_name = wm_class[1] or "Unknown"
						window_class = f"{class_name}.{instance_name}"

						problematic_patterns = [
							"Unknown.Unknown",
							"peony-qt-desktop",
							"desktop",
							"root"
						]

						for pattern in problematic_patterns:
							if pattern.lower() in window_class.lower():
								if self.debug:
									print(f"[DEBUG] ⚡ 检测到问题窗口类型: {window_class}", file=sys.stderr)
								disp.close()
								return True
				except Exception:
					pass

			disp.close()
			return False

		except Exception:
			return False

	def _should_skip_widget_analysis(self, x: int, y: int) -> bool:
		"""检查是否应该跳过控件识别以避免卡顿"""
		try:
			# 检查屏幕边缘区域
			if self._is_screen_edge_area(x, y):
				if self.debug:
					print(f"[DEBUG] ⚡ 跳过屏幕边缘区域的控件识别: ({x}, {y})", file=sys.stderr)
				return True

			# 检查问题窗口
			if self._is_problematic_window_at_point(x, y):
				if self.debug:
					print(f"[DEBUG] ⚡ 跳过可能导致卡顿的窗口控件识别: ({x}, {y})", file=sys.stderr)
				return True

			return False

		except Exception as e:
			if self.debug:
				print(f"[DEBUG] 快速检查失败，继续正常识别: {e}", file=sys.stderr)
			return False

	def _validate_widget_info(self, widget_info: Dict[str, Any], x: int, y: int) -> bool:
		"""验证控件信息的有效性"""
		if not widget_info:
			return False

		if widget_info.get('error'):
			return False

		# 检查坐标是否在控件范围内
		coords = widget_info.get('Coords', {})
		if coords:
			widget_x = coords.get('x', 0)
			widget_y = coords.get('y', 0)
			widget_w = coords.get('width', 0)
			widget_h = coords.get('height', 0)

			if not (widget_x <= x <= widget_x + widget_w and
					widget_y <= y <= widget_y + widget_h):
				if self.debug:
					print(
						f"[DEBUG] 控件坐标不匹配: 点击({x}, {y}) 不在控件范围({widget_x}, {widget_y}, {widget_w}, {widget_h})内",
						file=sys.stderr)
				return False

		return True

	def analyze_widget_at(self, x: int, y: int, interrupt_flag: threading.Event = None) -> Tuple[
		Optional[Dict[str, Any]], str]:
		"""
		分析指定坐标的控件信息（优化版）
		增加性能优化和中断支持
		"""
		if not self.uni:
			return None, "UNI模块未初始化"

		# 检查中断标志
		if interrupt_flag and interrupt_flag.is_set():
			return None, "操作被中断"

		# 检查缓存
		cache_key = (x, y)
		current_time = time.time()
		self._clear_cache()  # 每次分析前清理过期缓存
		if cache_key in self.cache:
			if self.debug:
				print(f"[DEBUG] 使用缓存的控件信息: {cache_key}", file=sys.stderr)
			return self.cache[cache_key]['data'], "从缓存获取"

		try:
			# 性能优化：快速检查是否需要跳过
			if self._should_skip_widget_analysis(x, y):
				return None, "跳过可能导致卡顿的控件识别"

			# 超时机制
			start_time = time.time()
			timeout = 3  # 3秒超时

			# 轻量级刷新缓存
			if hasattr(self.uni, 'desktop_cache') and isinstance(self.uni.desktop_cache,
																 dict) and 'desktop_object' in self.uni.desktop_cache:
				cache_entry = self.uni.desktop_cache['desktop_object']
				if time.time() - cache_entry.get('time', 0) > 0.05:  # 50ms超时
					del self.uni.desktop_cache['desktop_object']
					if self.debug:
						print(f"[DEBUG] 已清除过期的桌面缓存", file=sys.stderr)

			# 检查超时
			if time.time() - start_time > timeout:
				print(f"[WARNING] 控件识别超时", file=sys.stderr)
				return None, "控件识别超时"

			# 检查中断
			if interrupt_flag and interrupt_flag.is_set():
				return None, "操作被中断"

			# 获取控件信息（支持菜单识别）
			if hasattr(self.uni, 'kdk_getElement_Uni'):
				widget_result, info_text = self.uni.kdk_getElement_Uni(x, y, False, True)
				if widget_result and not widget_result.get('error'):
					widget = widget_result
				else:
					return None, info_text or "未找到控件"
			else:
				widget = self.uni.get_widget_at_position(x, y)
				if not widget:
					return None, f"在坐标({x}, {y})未找到控件"

				# 转换为统一格式
				widget = {
					'Name': widget.name,
					'Type': widget.role,
					'ProcessName': widget.process_name,
					'Coords': {
						'x': widget.x,
						'y': widget.y,
						'width': widget.width,
						'height': widget.height
					},
					'Parent': widget.parent.name if widget.parent else None,
					'Path': widget.get_path() if hasattr(widget, 'get_path') else None
				}

			# 验证控件信息
			if not self._validate_widget_info(widget, x, y):
				return None, "控件信息验证失败"

			# 存入缓存
			self.cache[cache_key] = {
				'data': widget,
				'timestamp': current_time
			}

			if self.debug:
				print(f"[DEBUG] 成功识别控件: {widget.get('Name')} ({widget.get('ProcessName')})", file=sys.stderr)
			return widget, "成功识别控件"

		except Exception as e:
			error_msg = f"分析控件时发生错误: {str(e)}"
			print(f"[ERROR] {error_msg}", file=sys.stderr)
			if self.debug:
				traceback.print_exc(file=sys.stderr)
			return None, error_msg

	def analyze_widget_at_with_new_app_detection(self, x: int, y: int, interrupt_flag: threading.Event = None) -> Tuple[
		Optional[Dict[str, Any]], str]:
		"""
		带新应用检测的控件识别（增强版）
		优化新应用控件识别成功率
		"""
		# 第一次尝试正常识别
		result, info_text = self.analyze_widget_at(x, y, interrupt_flag)

		# 检查中断
		if interrupt_flag and interrupt_flag.is_set():
			return None, "操作被中断"

		# 识别成功直接返回
		if result and not result.get('error'):
			return result, info_text

		# 识别失败，启动强化检测
		if self.debug:
			print(f"[DEBUG] 🔍 初次识别失败，启动新应用强化检测: 坐标({x}, {y})", file=sys.stderr)

		try:
			# 强制刷新AT-SPI注册表
			refresh_success = self.force_refresh_atspi_registry(interrupt_flag)
			if interrupt_flag and interrupt_flag.is_set():
				return None, "操作被中断"

			if not refresh_success and self.debug:
				print(f"[DEBUG] ⚠️ AT-SPI刷新失败，继续尝试识别", file=sys.stderr)

			# 第一次重新尝试
			result, info_text = self.analyze_widget_at(x, y, interrupt_flag)
			if interrupt_flag and interrupt_flag.is_set():
				return None, "操作被中断"

			if result and not result.get('error'):
				return result, "新应用检测成功"

			# 再次尝试刷新
			if self.debug:
				print(f"[DEBUG] 🔄 第一次强化检测失败，进行最后一次尝试...", file=sys.stderr)
			self.force_refresh_atspi_registry(interrupt_flag)
			if interrupt_flag and interrupt_flag.is_set():
				return None, "操作被中断"

			# 等待新应用初始化
			for i in range(30):
				if interrupt_flag and interrupt_flag.is_set():
					return None, "操作被中断"
				time.sleep(0.01)

			# 最终尝试
			result, info_text = self.analyze_widget_at(x, y, interrupt_flag)
			if result and not result.get('error'):
				return result, "延迟检测成功"
			else:
				error_msg = result.get('error', '未知错误') if result else '新应用检测失败'
				return None, error_msg

		except Exception as e:
			error_msg = f"新应用检测时发生错误: {e}"
			print(f"[ERROR] {error_msg}", file=sys.stderr)
			if self.debug:
				traceback.print_exc(file=sys.stderr)
			return None, error_msg


class AutoRecordingManager:
	"""全自动鼠标键盘事件录制管理器"""

	def __init__(
		self, debug: bool = False,
		enable_command_listener: bool = True,
		storage_path: str = "recordings",
		test_case_id: str = "",
		testcase_path: str = "",
		duration: int = 300,
		json_output: bool = False
	):
		enable_timestamped_print()  # 启用带时间戳的打印
		self.debug = debug
		self.storage_path = os.path.join(base_dir, storage_path)
		self.enable_command_listener = enable_command_listener  # 是否启用命令监听
		self.running = False
		self.test_case_id = test_case_id
		self.testcase_path = testcase_path
		self.duration = duration  # 录制时长（秒）
		self.json_output = json_output  # 是否启用JSON输出
		self.is_paused = False  # 添加暂停状态
		self.session: Optional[RecordingSession] = None
		self.event_queue = queue.Queue(maxsize=1000)  # 事件队列
		self.stop_event = threading.Event()  # 添加停止事件
		self._init_storage_path()
		# 初始化控件分析器 - 确保控件识别可用
		self.widget_analyzer = WidgetAnalyzer(debug=debug)

		# 菜单监听器由主进程管理，这里不需要初始化
		self.menu_listener = None

		# 初始化检测器（不需要菜单监听器引用）
		self.hover_detector = HoverDetector(
			widget_analyzer=self.widget_analyzer,
			debug=debug,
			event_queue=self.event_queue,
			menu_listener=None  # 不需要协调机制
		)
		self.drag_detector = DragDetector(debug=debug)
		self.double_click_detector = DoubleClickDetector(debug=debug)
		self.locator = LocatorUtils(debug=debug)
		self.locator_generator = None
		if LOCATOR_GENERATOR_AVAILABLE:
			try:
				self.locator_generator = LocatorGenerator(debug=debug)
				# 将locator_generator设置到LocatorUtils中
				self.locator.locator_generator = self.locator_generator
				if self.debug:
					print(f"[DEBUG] Locator生成器加载成功", file=sys.stderr)
			except Exception as e:
				print(f"[ERROR] Locator生成器初始化失败: {e}", file=sys.stderr)

		# 初始化命令处理器（如果启用）
		self.command_handler: Optional[CommandHandler] = None
		if self.enable_command_listener:
			self.command_handler = CommandHandler(
				debug=debug,
				stop_event=self.stop_event,
				pause_callback=self.pause_recording,
				resume_callback=self.resume_recording,
				status_callback=self.get_recording_status,
				update_bounds_callback=self._update_recorder_window_bounds
			)

		# 加载app_menu模块用于driver匹配
		self.app_menu_module = None
		self.current_testcase_path = testcase_path  # 当前测试用例路径
		self._load_app_menu_module(testcase_path)

		# 窗口过滤配置 - 排除录制控制器窗口的事件
		self.excluded_window_titles = [
			'记录器',  # GAT录制控制器窗口
			'operationRecord',  # 窗口名称
			'录制控制器',  # 可能的中文标题
			'Recording Controller',  # 可能的英文标题
		]
		self.excluded_window_classes = [
			'operationRecord',  # 窗口类名
		]

		# 录制控制器窗口位置缓存
		self.recorder_window_bounds = None
		self.recorder_window_bounds_cache_time = 0
		self.recorder_window_bounds_cache_duration = 2.0  # 缓存2秒

		# 菜单监听管理器已在上面初始化

		# 注册信号处理
		signal.signal(signal.SIGINT, self._handle_signal)
		signal.signal(signal.SIGTERM, self._handle_signal)

	def _init_storage_path(self):
		os.makedirs(self.storage_path, exist_ok=True)

	def _handle_signal(self, signum, frame):
		"""处理终止信号"""
		print(f"\n收到终止信号 {signum}，正在停止录制...")
		self.stop()
		sys.exit(0)


	def _monitor_timeout(self, start_time: float):
		"""监控录制超时"""
		while self.running:
			current_time = time.time()
			elapsed_time = current_time - start_time

			if elapsed_time >= self.duration:
				print(f"[INFO] 录制时间已达到设定时长 {self.duration} 秒，正在停止录制...")
				self.stop()
				break

			# 每秒检查一次
			time.sleep(1)

	def start(self):
		"""开始录制会话"""

		if not PYNPUT_AVAILABLE:
			print("[ERROR] pynput库不可用，无法启动录制", file=sys.stderr)
			return
		if self.testcase_path:
			self.locator.setup_locator_generator(self.testcase_path)
			# 同时设置locator_generator（如果可用）
			if self.locator_generator and hasattr(self.locator_generator, 'setup_locator_directory'):
				self.locator_generator.setup_locator_directory(self.testcase_path)

		self.running = True
		self.stop_event.clear()  # 清除停止事件
		start_time = time.time()
		self.session = RecordingSession(
			session_id=str(uuid.uuid4()),
			start_time=start_time,
			test_case_id=self.test_case_id
		)

		# 发送录制开始事件到OperationRecord窗口
		if self.json_output:
			# 使用与auto_recording_manager.py相同的格式
			output_json_event('recording_started', {
				'duration': self.duration,
				'start_time': start_time,
				'expected_end_time': start_time + self.duration if self.duration > 0 else None,
				'session_id': self.session.session_id,
				'test_case_id': self.test_case_id
			})
		else:
			print(f"[INFO] 开始录制会话: {self.session.session_id}")

		# 启动超时监控线程（如果设置了duration）
		if self.duration > 0:
			self.timeout_monitor_thread = threading.Thread(
				target=self._monitor_timeout,
				args=(start_time,),
				daemon=True
			)
			self.timeout_monitor_thread.start()
			print(f"[INFO] 录制将在 {self.duration} 秒后自动停止")

		# 检查关键组件状态
		if not self.widget_analyzer.uni:
			print("[WARNING] 控件分析器初始化不完整，可能无法识别控件", file=sys.stderr)
		else:
			print("[INFO] 控件分析器已准备就绪", file=sys.stderr)

		if not self.hover_detector.highlight_renderer:
			print("[WARNING] 高亮渲染器未初始化，将无法显示控件高亮", file=sys.stderr)
		else:
			print("[INFO] 高亮渲染器已准备就绪", file=sys.stderr)

		# 启动事件处理线程
		self.event_processor_thread = threading.Thread(
			target=self._process_events,
			daemon=True
		)
		self.event_processor_thread.start()

		# 启动命令监听器（如果启用）
		if self.enable_command_listener and self.command_handler:
			self.command_handler.start()
			print("[INFO] 命令监听器已启动", file=sys.stderr)
		elif self.enable_command_listener:
			print("[WARNING] 命令监听器初始化失败", file=sys.stderr)
		else:
			print("[INFO] 命令监听器已禁用", file=sys.stderr)

		# 菜单监听器已在start()方法开始时启动

		# 启动检测器
		self.hover_detector.start()

		# 启动鼠标监听器
		self.mouse_listener = pynput_mouse.Listener(
			on_move=self._on_mouse_move,
			on_click=self._on_mouse_click,
			on_scroll=self._on_mouse_scroll
		)
		self.mouse_listener.start()

		# 启动键盘监听器
		self.keyboard_listener = pynput_keyboard.Listener(
			on_press=self._on_key_press,
			on_release=self._on_key_release
		)
		self.keyboard_listener.start()

		print("[INFO] 录制已启动，按Ctrl+C停止")
		print("[INFO] 将鼠标悬停在控件上0.5秒可看到高亮，悬停3秒将录制控件信息")
		if self.enable_command_listener:
			print("[INFO] 可通过stdin发送命令: pause/resume/stop/status")
		else:
			print("[INFO] 命令监听已禁用")

	def stop(self):
		"""停止录制会话"""
		if not self.running:
			return

		self.running = False
		self.stop_event.set()  # 设置停止事件
		print(f"[INFO] 停止录制会话: {self.session.session_id if self.session else '未知'}")

		# 停止监听器
		if hasattr(self, 'mouse_listener') and self.mouse_listener.is_alive():
			self.mouse_listener.stop()
		if hasattr(self, 'keyboard_listener') and self.keyboard_listener.is_alive():
			self.keyboard_listener.stop()

		# 菜单监听器由主进程管理，这里不需要停止

		# 停止检测器
		self.hover_detector.stop()
		self.hover_detector.cleanup()

		# 等待事件处理完成
		if hasattr(self, 'event_processor_thread') and self.event_processor_thread.is_alive():
			self.event_queue.put(('shutdown', None))  # 发送终止信号
			self.event_processor_thread.join(timeout=5)

		# 保存会话结束时间
		if self.session:
			self.session.end_time = time.time()
			filename = f"session_{self.session.session_id}.json"
			print(f"[INFO] 正在保存录制数据到: {self.storage_path}/{filename}")
			save_data_json(self.session.record_events, f'{self.storage_path}/{filename}')
			print(f"[INFO] 录制完成，会话时长: {self.session.end_time - self.session.start_time:.2f}秒")
			print(
				f"[INFO] 录制事件统计: 鼠标事件={len(self.session.mouse_events)}, 键盘事件={len(self.session.keyboard_events)}")

	def pause_recording(self) -> bool:
		"""
		暂停录制

		Returns:
			是否成功暂停录制
		"""
		if not self.running:
			print("[WARNING] 录制未在进行中，无法暂停", file=sys.stderr)
			return False

		if self.is_paused:
			print("[WARNING] 录制已处于暂停状态", file=sys.stderr)
			return False

		try:
			if self.debug:
				print("[DEBUG] 开始暂停录制...", file=sys.stderr)

			# 设置暂停状态
			self.is_paused = True

			# 暂停悬停检测器，停止高亮显示
			if self.hover_detector:
				self.hover_detector.stop()
				# 清除当前的高亮显示
				if hasattr(self.hover_detector, 'highlight_renderer') and self.hover_detector.highlight_renderer:
					try:
						self.hover_detector.highlight_renderer.clear_highlight()
						if self.debug:
							print("[DEBUG] 已清除当前高亮显示", file=sys.stderr)
					except Exception as e:
						if self.debug:
							print(f"[DEBUG] 清除高亮显示时出错: {e}", file=sys.stderr)
				if self.debug:
					print("[DEBUG] 悬停检测器已暂停", file=sys.stderr)

			if self.debug:
				print("[DEBUG] 录制已暂停", file=sys.stderr)

			print("[INFO] 录制已暂停", file=sys.stderr)
			return True

		except Exception as e:
			print(f"[ERROR] 暂停录制失败: {e}", file=sys.stderr)
			if self.debug:
				import traceback
				traceback.print_exc(file=sys.stderr)
			return False

	def resume_recording(self) -> bool:
		"""
		恢复录制

		Returns:
			是否成功恢复录制
		"""
		if not self.running:
			print("[WARNING] 录制未在进行中，无法恢复", file=sys.stderr)
			return False

		if not self.is_paused:
			print("[WARNING] 录制未处于暂停状态", file=sys.stderr)
			return False

		try:
			if self.debug:
				print("[DEBUG] 开始恢复录制...", file=sys.stderr)

			# 清除暂停状态
			self.is_paused = False

			# 恢复悬停检测器，重新启用高亮显示
			if self.hover_detector:
				self.hover_detector.start()
				if self.debug:
					print("[DEBUG] 悬停检测器已恢复", file=sys.stderr)

			if self.debug:
				print("[DEBUG] 录制已恢复", file=sys.stderr)

			print("[INFO] 录制已恢复", file=sys.stderr)
			return True

		except Exception as e:
			print(f"[ERROR] 恢复录制失败: {e}", file=sys.stderr)
			if self.debug:
				import traceback
				traceback.print_exc(file=sys.stderr)
			return False

	def get_recording_status(self) -> Dict[str, Any]:
		"""
		获取录制状态信息

		Returns:
			状态信息字典
		"""
		status = {
			'is_recording': self.running,
			'is_paused': self.is_paused,
			'current_session_id': self.session.session_id if self.session else None,
			'start_time': self.session.start_time if self.session else None,
		}

		if self.session:
			status['recording_duration'] = time.time() - self.session.start_time

		return status
	def _update_recorder_window_bounds(self, bounds: Dict[str, int]):
		"""
		更新录制器窗口边界

		Args:
			bounds: 包含窗口边界的字典，如 {'x': 0, 'y': 0, 'width': 1920, 'height': 1080}
		"""
		if self.debug:
			print(f"[DEBUG] 收到窗口边界更新命令: {bounds}", file=sys.stderr)
		# 实际应用中可能需要具体实现窗口边界限制逻辑
		# 这里只是简单记录日志

	def _on_mouse_move(self, x: int, y: int):
		"""处理鼠标移动事件"""
		if not self.running:
			return

		# 通知悬停检测器
		self.hover_detector.on_mouse_move(x, y)

		# 检查拖动
		drag_event = self.drag_detector.on_mouse_move(x, y)
		if drag_event:
			try:
				print(f'[INFO] 将拖动移动事件加入到事件队列')
				self.event_queue.put(('mouse', drag_event), block=False)
			except queue.Full:
				print("[ERROR] 事件队列已满，丢弃拖动移动事件", file=sys.stderr)

	def _on_mouse_click(self, x: int, y: int, button: Any, pressed: bool):
		"""处理鼠标点击事件"""
		if not self.running:
			return

		# 检查是否应该排除此窗口的事件
		if self._should_exclude_window(x, y):
			if self.debug:
				print(f"[FILTER] 🚫 跳过录制控制器窗口的鼠标事件: 位置=({x}, {y})", file=sys.stderr)
			return

		# 转换按钮为字符串
		button_str = str(button).split('.')[-1]  # 如 'left', 'right'

		if pressed:
			# 鼠标按下 - 记录拖动起始点
			self.drag_detector.on_mouse_press(x, y, button_str)
		else:
			# 鼠标释放 - 检查拖动结束
			drag_end_event = self.drag_detector.on_mouse_release(x, y, button_str)
			if drag_end_event:
				try:
					print(f'[INFO] 将拖动结束事件加入到事件队列')
					self.event_queue.put(('mouse', drag_end_event), block=False)
					return
				except queue.Full:
					print("[ERROR] 事件队列已满，丢弃拖动结束事件", file=sys.stderr)
					return

			# 检测双击
			click_event = self.double_click_detector.on_mouse_click(x, y, button_str, time.time())
			if click_event:
				# 尝试获取悬停缓存的控件信息
				widget_info = self.hover_detector.get_cached_widget_info(x, y)
				if widget_info:
					click_event.widget_info = widget_info
					# 打印点击的控件信息
					print("\n" + "*" * 60, file=sys.stderr)
					print(f"[CLICK] 点击控件信息:", file=sys.stderr)
					print(f"  名称: {widget_info.get('Name', 'Unknown')}", file=sys.stderr)
					print(f"  位置: ({x}, {y})", file=sys.stderr)
					print("*" * 60 + "\n", file=sys.stderr)

				try:
					print(f'[INFO] 将点击事件加入到事件队列')
					self.event_queue.put(('mouse', click_event), block=False)
				except queue.Full:
					print("[ERROR] 事件队列已满，丢弃点击事件", file=sys.stderr)

	def _on_mouse_scroll(self, x: int, y: int, dx: int, dy: int):
		"""处理鼠标滚轮事件"""
		if not self.running:
			return

		scroll_event = MouseEvent(
			timestamp=time.time(),
			event_type='scroll',
			x=x,
			y=y,
			scroll_dx=dx,
			scroll_dy=dy
		)

		try:
			print(f'[INFO] 将滚轮事件加入到事件队列')
			self.event_queue.put(('mouse', scroll_event), block=False)
		except queue.Full:
			print("[ERROR] 事件队列已满，丢弃滚轮事件", file=sys.stderr)

	def _on_key_press(self, key: Any):
		"""处理键盘按下事件"""
		if not self.running:
			return
		self._handle_key_event(key, 'press')

	def _on_key_release(self, key: Any):
		"""处理键盘释放事件"""
		if not self.running:
			return
		self._handle_key_event(key, 'release')

	def _handle_key_event(self, key: Any, event_type: str):
		"""处理键盘事件"""
		try:
			key_str = key.char if hasattr(key, 'char') and key.char else str(key).split('.')[-1]
			key_code = key.vk if hasattr(key, 'vk') else None

			# 处理修饰键
			modifiers = []
			if hasattr(key, 'modifiers'):
				modifiers = [str(m).split('.')[-1] for m in key.modifiers]

			keyboard_event = KeyboardEvent(
				timestamp=time.time(),
				event_type=event_type,
				key=key_str,
				key_code=key_code,
				modifiers=modifiers
			)

			try:
				print(f'[INFO] 将键盘事件加入到事件队列')
				self.event_queue.put(('keyboard', keyboard_event), block=False)

			except queue.Full:
				print(f"[ERROR] 事件队列已满，丢弃键盘{event_type}事件", file=sys.stderr)

		except Exception as e:
			print(f"[ERROR] 处理键盘事件失败: {e}", file=sys.stderr)
			if self.debug:
				import traceback
				traceback.print_exc(file=sys.stderr)

	def _process_events(self):
		"""处理事件队列中的事件"""
		print("[INFO] 事件处理器已启动")
		while self.running:
			try:
				event_type, event = self.event_queue.get(block=True, timeout=1)

				if event_type == 'shutdown':
					break

				# 如果录制已暂停，跳过事件处理但仍需标记任务完成
				if self.is_paused:
					if self.debug:
						print(f"[DEBUG] 录制已暂停，跳过事件: {event_type}", file=sys.stderr)
					self.event_queue.task_done()
					continue

				if self.session:
					if event_type == 'mouse' and isinstance(event, MouseEvent):
						self.session.mouse_events.append(event)
						# 打印鼠标事件信息 - 不再记录悬停事件
						if event.event_type == 'hover':
							# 不再记录悬停事件
							if self.debug:
								widget_name = event.widget_info.get('Name', 'Unknown') if event.widget_info else 'Unknown'
								print(f"[DEBUG] 忽略悬停事件记录: 位置=({event.x}, {event.y}), 控件={widget_name}", file=sys.stderr)
							# 从session中移除悬停事件
							if self.session.mouse_events and self.session.mouse_events[-1] == event:
								self.session.mouse_events.pop()
						elif event.event_type in ['click', 'double_click', 'right_click']:
							widget_name = event.widget_info.get('Name', 'Unknown') if event.widget_info else 'Unknown'
							if self.json_output:
								# 使用与前端期望的JSON输出格式
								self._output_mouse_click_json(event)
							else:
								print(
									f"[EVENT] 记录{event.event_type}事件: 位置=({event.x}, {event.y}), 控件={widget_name}",
									file=sys.stderr)
						elif event.event_type == 'drag_end':
							# 只处理拖拽结束事件，与auto_recording_manager.py保持一致
							if self.json_output:
								# 输出拖拽事件
								self._output_mouse_drag_json(event)
							else:
								print(f"[EVENT] 记录拖拽结束事件: 起始({event.drag_start_x}, {event.drag_start_y}) 结束({event.x}, {event.y}) 距离={event.drag_distance:.1f}px", file=sys.stderr)
						elif event.event_type == 'drag_start':
							# 拖拽开始事件只用于调试，不输出JSON
							if self.debug:
								print(f"[DEBUG] 拖拽开始: 位置=({event.x}, {event.y}) 按钮={event.button}", file=sys.stderr)
						elif event.event_type == 'drag_move':
							# 拖拽移动事件不处理，避免事件过多，与auto_recording_manager.py保持一致
							if self.debug:
								print(f"[DEBUG] 忽略拖拽移动事件: 位置=({event.x}, {event.y})", file=sys.stderr)
					elif event_type == 'keyboard' and isinstance(event, KeyboardEvent):
						self.session.keyboard_events.append(event)
						if self.json_output:
							# 使用与auto_recording_manager.py相同的JSON输出格式
							self._output_keyboard_json(event)
						else:
							print(f"[EVENT] 记录键盘{event.event_type}事件: 键={event.key}", file=sys.stderr)
					self.session.record_events.append(asdict(event))
				self.event_queue.task_done()

			except queue.Empty:
				continue
			except Exception as e:
				print(f"[ERROR] 事件处理失败: {e}", file=sys.stderr)
				if self.debug:
					import traceback
					traceback.print_exc(file=sys.stderr)

		print("[INFO] 事件处理器已停止")

	def _output_mouse_click_json(self, event: MouseEvent):
		"""输出鼠标点击事件的JSON格式数据 - 与前端期望的格式一致"""
		try:
			# 调试信息：确认方法被调用
			if self.debug:
				print(f"[DEBUG] 准备输出{event.event_type}JSON: 位置({event.x}, {event.y}) 按钮={event.button}", file=sys.stderr)

			# 构建事件数据
			event_data = {
				'event_type': event.event_type,  # 支持 'click', 'right_click', 'double_click'
				'timestamp': event.timestamp,
				'x': event.x,
				'y': event.y,
				'button': event.button,
				'pressed': event.pressed,
				'widget_info': None
			}

			# 如果是双击事件，添加双击相关信息
			if event.event_type == 'double_click':
				event_data['click_count'] = getattr(event, 'click_count', 2)
				event_data['time_since_last_click'] = getattr(event, 'time_since_last_click', 0)

			# 添加控件信息
			widget_info = getattr(event, 'widget_info', None)
			if widget_info and not widget_info.get('error'):
				# 添加is_valid字段，这是前端判断控件有效性的关键字段
				widget_info_copy = widget_info.copy()
				widget_info_copy['is_valid'] = True

				# 添加driver字段，根据ProcessName匹配app_menu.py中的exec值
				process_name = widget_info.get('ProcessName', '')
				driver = self.get_driver_from_process_name(process_name)
				widget_info_copy['driver'] = driver

				event_data['widget_info'] = widget_info_copy
				if self.debug:
					widget_name = widget_info.get('Name', 'Unknown')
					print(f"[DEBUG] 控件信息已添加到JSON: {widget_name}, driver: {driver}", file=sys.stderr)
			else:
				# 如果没有控件信息或有错误，设置is_valid为False
				if widget_info:
					widget_info_copy = widget_info.copy()
					widget_info_copy['is_valid'] = False
					event_data['widget_info'] = widget_info_copy

			# 根据事件类型确定JSON事件类型，与前端期望的格式一致
			json_event_type = 'mouse_click'  # 默认
			if event.event_type == 'right_click':
				json_event_type = 'mouse_right_click'
			elif event.event_type == 'double_click':
				json_event_type = 'mouse_double_click'
			elif event.event_type == 'click':
				json_event_type = 'mouse_click'

			# 输出JSON事件 - 使用前端期望的事件类型
			output_json_event(json_event_type, event_data)

			# 调试信息：确认JSON输出成功
			if self.debug:
				widget_status = "有控件" if event_data['widget_info'] else "无控件"
				print(f"[DEBUG] 成功输出{event.event_type}JSON: {widget_status}", file=sys.stderr)

		except Exception as e:
			if self.debug:
				print(f"[ERROR] 输出鼠标点击JSON失败: {e}", file=sys.stderr)

	def _output_mouse_drag_json(self, event: MouseEvent):
		"""输出鼠标拖拽事件的JSON格式数据 - 只处理drag_end事件，与auto_recording_manager.py保持一致"""
		try:
			# 只处理拖拽结束事件
			if event.event_type != 'drag_end':
				if self.debug:
					print(f"[DEBUG] 跳过非drag_end事件: {event.event_type}", file=sys.stderr)
				return

			# 调试信息：确认方法被调用
			if self.debug:
				print(f"[DEBUG] 准备输出拖拽结束JSON: 起始({event.drag_start_x}, {event.drag_start_y}) 结束({event.x}, {event.y})", file=sys.stderr)

			# 构建拖拽事件数据 - 使用mouse_drag作为事件类型，与auto_recording_manager.py保持一致
			event_data = {
				'event_type': 'mouse_drag',  # 统一使用mouse_drag
				'timestamp': event.timestamp,
				'start_x': event.drag_start_x,
				'start_y': event.drag_start_y,
				'end_x': event.x,
				'end_y': event.y,
				'button': event.button,
				'distance': event.drag_distance,
				'duration': event.drag_duration,
				'path_points': len(event.drag_path) if event.drag_path else 0,
				'widget_info': None
			}



			# 添加控件信息
			widget_info = getattr(event, 'widget_info', None)
			if widget_info and not widget_info.get('error'):
				# 添加is_valid字段，这是前端判断控件有效性的关键字段
				widget_info_copy = widget_info.copy()
				widget_info_copy['is_valid'] = True

				# 添加driver字段，根据ProcessName匹配app_menu.py中的exec值
				process_name = widget_info.get('ProcessName', '')
				driver = self.get_driver_from_process_name(process_name)
				widget_info_copy['driver'] = driver

				event_data['widget_info'] = widget_info_copy
				if self.debug:
					widget_name = widget_info.get('Name', 'Unknown')
					print(f"[DEBUG] 拖拽控件信息已添加到JSON: {widget_name}, driver: {driver}", file=sys.stderr)
			else:
				# 如果没有控件信息或有错误，设置is_valid为False
				if widget_info:
					widget_info_copy = widget_info.copy()
					widget_info_copy['is_valid'] = False
					event_data['widget_info'] = widget_info_copy

			# 输出JSON事件 - 使用前端期望的事件类型
			output_json_event('mouse_drag', event_data)

			# 调试信息：确认JSON输出成功
			if self.debug:
				widget_status = "有控件" if event_data['widget_info'] else "无控件"
				print(f"[DEBUG] 成功输出拖拽JSON: 起始({event.drag_start_x}, {event.drag_start_y}) 结束({event.x}, {event.y}) {widget_status}", file=sys.stderr)

		except Exception as e:
			if self.debug:
				print(f"[ERROR] 输出鼠标拖拽JSON失败: {e}", file=sys.stderr)

	def _output_keyboard_json(self, event: KeyboardEvent):
		"""输出键盘事件的JSON格式数据 - 与auto_recording_manager.py相同的实现"""
		try:
			# 调试信息：确认方法被调用
			if self.debug:
				print(f"[DEBUG] 准备输出键盘JSON: 按键={event.key}", file=sys.stderr)

			# 构建键盘事件数据
			event_data = {
				'event_type': 'key_press',
				'timestamp': event.timestamp,
				'key': event.key,
				'modifiers': getattr(event, 'modifiers', []) or [],
				'key_combination': self._format_key_combination(event.key, getattr(event, 'modifiers', []))
			}

			# 输出JSON事件
			output_json_event('keyboard', event_data)

			# 调试信息：确认JSON输出成功
			if self.debug:
				key_combo = event_data['key_combination']
				print(f"[DEBUG] 成功输出键盘JSON: 组合键={key_combo}", file=sys.stderr)

		except Exception as e:
			if self.debug:
				print(f"[ERROR] 输出键盘JSON失败: {e}", file=sys.stderr)

	def _format_key_combination(self, key: str, modifiers: List[str]) -> str:
		"""格式化按键组合字符串"""
		if not modifiers:
			return key

		# 排序修饰键以保持一致性
		sorted_modifiers = sorted(modifiers)
		return '+'.join(sorted_modifiers + [key])

	def _load_app_menu_module(self, testcase_path: str = None):
		"""
		加载app_menu模块

		Args:
			testcase_path: 测试用例路径，如果提供则优先从该路径加载app_menu.py
		"""
		try:
			app_menu_paths = []

			# 如果提供了测试用例路径，优先从该路径查找
			if testcase_path:
				# 测试用例路径通常指向testcase目录，app_menu.py在其父目录
				testcase_dir = os.path.dirname(testcase_path) if os.path.isfile(testcase_path) else testcase_path

				# 构建可能的app_menu.py路径
				possible_paths = [
					os.path.join(testcase_dir, "app_menu.py"),  # 同级目录
					os.path.join(os.path.dirname(testcase_dir), "app_menu.py"),  # 父目录
					os.path.join(testcase_dir, "..", "app_menu.py"),  # 相对父目录
				]

				for path in possible_paths:
					normalized_path = os.path.normpath(path)
					if os.path.exists(normalized_path):
						app_menu_paths.append(normalized_path)
						if self.debug:
							print(f"[DEBUG] 从测试用例路径找到app_menu.py: {normalized_path}", file=sys.stderr)

			# 添加默认的备用路径
			app_menu_paths.extend([
				"KylinRobot-v2/app_menu.py",
				"../KylinRobot-v2/app_menu.py",
				"../../KylinRobot-v2/app_menu.py",
				"extensions/KylinRobot-v2/app_menu.py",
				"kylinrobot-ide-arm64-remote-display-enhanced/resources/app/KylinRobot-v2/app_menu.py"
			])

			for app_menu_path in app_menu_paths:
				if os.path.exists(app_menu_path):
					spec = importlib.util.spec_from_file_location("app_menu", app_menu_path)
					if spec and spec.loader:
						self.app_menu_module = importlib.util.module_from_spec(spec)
						spec.loader.exec_module(self.app_menu_module)
						if self.debug:
							print(f"[DEBUG] app_menu模块加载成功: {app_menu_path}", file=sys.stderr)
						return

			if self.debug:
				print("[DEBUG] 未找到app_menu.py文件，driver匹配功能将不可用", file=sys.stderr)

		except Exception as e:
			if self.debug:
				print(f"[DEBUG] 加载app_menu模块失败: {e}", file=sys.stderr)

	def get_driver_from_process_name(self, process_name: str) -> str:
		"""
		根据ProcessName从app_menu.py中获取对应的driver值

		Args:
			process_name: 控件信息中的ProcessName字段

		Returns:
			str: 匹配的driver值，如果未找到则返回process_name本身
		"""
		if not self.app_menu_module or not process_name:
			return process_name or 'unknown'

		try:
			# 获取app_menu模块中的所有类
			for attr_name in dir(self.app_menu_module):
				attr = getattr(self.app_menu_module, attr_name)
				# 检查是否是dataclass类
				if hasattr(attr, '__dataclass_fields__') and hasattr(attr, 'exec'):
					try:
						# 创建类实例以获取exec值
						instance = attr()
						exec_value = instance.exec

						# 直接匹配
						if exec_value == process_name:
							if self.debug:
								print(f"[DEBUG] 精确匹配找到driver: {process_name} -> {exec_value}", file=sys.stderr)
							return exec_value

						# 处理截断情况：如果process_name是exec_value的前缀
						if process_name and exec_value.startswith(process_name):
							if self.debug:
								print(f"[DEBUG] 前缀匹配找到driver: {process_name} -> {exec_value}", file=sys.stderr)
							return exec_value

						# 处理截断情况：如果exec_value是process_name的前缀
						if exec_value and process_name.startswith(exec_value):
							if self.debug:
								print(f"[DEBUG] 反向前缀匹配找到driver: {process_name} -> {exec_value}", file=sys.stderr)
							return exec_value

					except Exception as e:
						# 某些类可能无法实例化，跳过
						continue

			# 如果没有找到匹配，尝试自动创建新的driver类
			if self.debug:
				print(f"[DEBUG] 未找到匹配的driver，尝试自动创建: {process_name}", file=sys.stderr)

			# 自动创建新的driver类
			if self._auto_create_driver_class(process_name):
				if self.debug:
					print(f"[DEBUG] 成功创建新driver类: {process_name}", file=sys.stderr)
				return process_name
			else:
				if self.debug:
					print(f"[DEBUG] 创建driver类失败，使用原始ProcessName: {process_name}", file=sys.stderr)
				return process_name

		except Exception as e:
			if self.debug:
				print(f"[DEBUG] driver匹配过程出错: {e}", file=sys.stderr)
			return process_name or 'unknown'

	def _auto_create_driver_class(self, process_name: str) -> bool:
		"""
		自动在app_menu.py中创建新的driver类

		Args:
			process_name: 未匹配的进程名

		Returns:
			bool: 是否创建成功
		"""
		if not process_name or not self.app_menu_module:
			return False

		try:
			# 查找app_menu.py文件路径，优先使用testcase_path
			app_menu_paths = []

			# 如果有当前测试用例路径，优先从该路径查找
			if self.current_testcase_path:
				testcase_dir = os.path.dirname(self.current_testcase_path) if os.path.isfile(self.current_testcase_path) else self.current_testcase_path

				# 构建可能的app_menu.py路径
				possible_paths = [
					os.path.join(testcase_dir, "app_menu.py"),  # 同级目录
					os.path.join(os.path.dirname(testcase_dir), "app_menu.py"),  # 父目录
					os.path.join(testcase_dir, "..", "app_menu.py"),  # 相对父目录
				]

				for path in possible_paths:
					normalized_path = os.path.normpath(path)
					if os.path.exists(normalized_path):
						app_menu_paths.append(normalized_path)
						if self.debug:
							print(f"[DEBUG] 从测试用例路径找到app_menu.py用于自动创建: {normalized_path}", file=sys.stderr)

			# 添加默认的备用路径
			app_menu_paths.extend([
				"KylinRobot-v2/app_menu.py",
				"../KylinRobot-v2/app_menu.py",
				"../../KylinRobot-v2/app_menu.py",
				"extensions/KylinRobot-v2/app_menu.py",
				"kylinrobot-ide-arm64-remote-display-enhanced/resources/app/KylinRobot-v2/app_menu.py"
			])

			app_menu_file_path = None
			for path in app_menu_paths:
				if os.path.exists(path):
					app_menu_file_path = path
					break

			if not app_menu_file_path:
				if self.debug:
					print("[DEBUG] 未找到app_menu.py文件，无法自动创建driver类", file=sys.stderr)
				return False

			# 生成类名（将process_name转换为合适的类名）
			class_name = self._generate_class_name(process_name)

			# 检查类名是否已存在
			if hasattr(self.app_menu_module, class_name):
				if self.debug:
					print(f"[DEBUG] 类名 {class_name} 已存在，跳过创建", file=sys.stderr)
				return True

			# 生成新的类定义
			new_class_definition = self._generate_class_definition(class_name, process_name)

			# 将新类追加到app_menu.py文件
			with open(app_menu_file_path, 'a', encoding='utf-8') as f:
				f.write('\n\n' + new_class_definition)

			if self.debug:
				print(f"[DEBUG] 成功在 {app_menu_file_path} 中创建新类: {class_name}", file=sys.stderr)

			# 重新加载app_menu模块以包含新类
			self._reload_app_menu_module()

			return True

		except Exception as e:
			if self.debug:
				print(f"[DEBUG] 自动创建driver类失败: {e}", file=sys.stderr)
			return False

	def _generate_class_name(self, process_name: str) -> str:
		"""
		根据进程名生成合适的类名

		Args:
			process_name: 进程名

		Returns:
			str: 生成的类名
		"""
		# 移除特殊字符，转换为驼峰命名
		clean_name = ''.join(c for c in process_name if c.isalnum() or c in '-_')

		# 按照连字符和下划线分割，然后转换为驼峰命名
		parts = clean_name.replace('-', '_').split('_')
		class_name = ''.join(word.capitalize() for word in parts if word)

		# 确保类名不为空且以字母开头
		if not class_name or not class_name[0].isalpha():
			class_name = 'App' + class_name

		return class_name

	def _generate_class_definition(self, class_name: str, process_name: str) -> str:
		"""
		生成新的dataclass类定义

		Args:
			class_name: 类名
			process_name: 进程名

		Returns:
			str: 类定义字符串
		"""
		return f'''@dataclass(frozen=True)
class {class_name}:
	"""
	自动生成的应用类 - {process_name}
	"""
	exec: str = '{process_name}' '''

	def _reload_app_menu_module(self):
		"""重新加载app_menu模块"""
		try:
			# 重新加载模块，使用当前的测试用例路径
			self._load_app_menu_module(self.current_testcase_path)
			if self.debug:
				print("[DEBUG] app_menu模块重新加载成功", file=sys.stderr)
		except Exception as e:
			if self.debug:
				print(f"[DEBUG] 重新加载app_menu模块失败: {e}", file=sys.stderr)

	def _get_recorder_window_bounds(self) -> dict:
		"""
		获取录制控制器窗口的位置和大小信息

		Returns:
			dict: 包含窗口位置信息的字典，格式为 {'x': int, 'y': int, 'width': int, 'height': int}
				 如果未找到窗口则返回None
		"""
		current_time = time.time()

		# 检查缓存是否有效
		if (self.recorder_window_bounds and
			current_time - self.recorder_window_bounds_cache_time < self.recorder_window_bounds_cache_duration):
			return self.recorder_window_bounds

		try:
			import pyatspi

			desktop = pyatspi.Registry.getDesktop(0)
			if not desktop:
				return None

			# 遍历所有应用程序查找录制控制器窗口
			for i in range(desktop.childCount):
				try:
					app = desktop.getChildAtIndex(i)
					if not app:
						continue

					# 检查应用程序名称
					app_name = getattr(app, 'name', '') or ''

					# 遍历应用程序的窗口
					for j in range(app.childCount):
						try:
							window = app.getChildAtIndex(j)
							if not window:
								continue

							window_title = getattr(window, 'name', '') or ''

							# 检查是否是录制控制器窗口
							is_recorder_window = False
							for excluded_title in self.excluded_window_titles:
								if excluded_title in window_title or excluded_title in app_name:
									is_recorder_window = True
									break

							if is_recorder_window and hasattr(window, 'queryComponent'):
								component = window.queryComponent()
								if component:
									extents = component.getExtents(pyatspi.DESKTOP_COORDS)
									bounds = {
										'x': extents.x,
										'y': extents.y,
										'width': extents.width,
										'height': extents.height
									}

									# 更新缓存
									self.recorder_window_bounds = bounds
									self.recorder_window_bounds_cache_time = current_time

									if self.debug:
										print(f"[DEBUG] 📍 找到录制控制器窗口: 标题='{window_title}' 位置=({bounds['x']}, {bounds['y']}) 大小=({bounds['width']}x{bounds['height']})", file=sys.stderr)

									return bounds
						except Exception:
							continue
				except Exception:
					continue

		except Exception as e:
			if self.debug:
				print(f"[DEBUG] 获取录制控制器窗口位置失败: {e}", file=sys.stderr)

		return None

	def _is_point_in_recorder_window(self, x: int, y: int) -> bool:
		"""
		检查指定坐标是否在录制控制器窗口内

		Args:
			x: 鼠标X坐标
			y: 鼠标Y坐标

		Returns:
			bool: True表示坐标在录制控制器窗口内，False表示不在
		"""
		bounds = self._get_recorder_window_bounds()
		if not bounds:
			return False

		# 检查坐标是否在窗口范围内
		in_window = (bounds['x'] <= x <= bounds['x'] + bounds['width'] and
					bounds['y'] <= y <= bounds['y'] + bounds['height'])

		if in_window and self.debug:
			print(f"[DEBUG] 🎯 坐标({x}, {y})位于录制控制器窗口内: 窗口范围=({bounds['x']}, {bounds['y']}, {bounds['x'] + bounds['width']}, {bounds['y'] + bounds['height']})", file=sys.stderr)

		return in_window

	def _should_exclude_window(self, x: int, y: int) -> bool:
		"""
		检查指定坐标的窗口是否应该被排除录制

		Args:
			x: 鼠标X坐标
			y: 鼠标Y坐标

		Returns:
			bool: True表示应该排除，False表示应该录制
		"""
		# 首先检查坐标是否在录制控制器窗口内（最精确的方法）
		if self._is_point_in_recorder_window(x, y):
			if self.debug:
				print(f"[DEBUG] 🚫 排除录制控制器窗口内的事件: 坐标=({x}, {y})", file=sys.stderr)
			return True

		# 如果坐标检查失败，使用原有的窗口标题检查作为备用方法
		try:
			import pyatspi

			# 使用更高效的方法：直接获取指定坐标的可访问对象
			try:
				# 获取指定坐标的顶层窗口
				desktop = pyatspi.Registry.getDesktop(0)
				if hasattr(desktop, 'getAccessibleAtPoint'):
					accessible = desktop.getAccessibleAtPoint(x, y, pyatspi.DESKTOP_COORDS)
				else:
					# 如果不支持getAccessibleAtPoint，直接使用备用方法
					raise AttributeError("getAccessibleAtPoint not supported")

				# 向上遍历到窗口级别
				current = accessible
				window_found = None
				max_depth = 10  # 限制遍历深度，避免无限循环

				for _ in range(max_depth):
					if not current:
						break

					# 检查是否是窗口或框架
					role = current.getRole()
					if role in [pyatspi.ROLE_WINDOW, pyatspi.ROLE_FRAME, pyatspi.ROLE_DIALOG]:
						window_found = current
						break

					# 向上查找父对象
					try:
						current = current.parent
					except:
						break

				if window_found:
					# 检查窗口标题
					window_title = getattr(window_found, 'name', '') or ''
					if window_title:
						for excluded_title in self.excluded_window_titles:
							if excluded_title in window_title:
								if self.debug:
									print(f"[DEBUG] 🚫 排除录制控制器窗口事件(标题匹配): 标题='{window_title}' 坐标=({x}, {y})", file=sys.stderr)
								return True

					# 检查应用程序名称（窗口的父应用）
					try:
						app = window_found.getApplication()
						if app:
							app_name = getattr(app, 'name', '') or ''
							if app_name:
								for excluded_title in self.excluded_window_titles:
									if excluded_title in app_name:
										if self.debug:
											print(f"[DEBUG] 🚫 排除录制控制器应用事件(应用名匹配): 应用='{app_name}' 坐标=({x}, {y})", file=sys.stderr)
										return True
					except:
						pass

			except Exception as e:
				if self.debug:
					print(f"[DEBUG] 快速窗口检查失败，使用备用方法: {e}", file=sys.stderr)

				# 备用方法：遍历所有窗口（性能较低但更可靠）
				return self._should_exclude_window_fallback(x, y)

		except Exception as e:
			if self.debug:
				print(f"[DEBUG] 窗口过滤检查失败: {e}", file=sys.stderr)

		return False

	def _should_exclude_window_fallback(self, x: int, y: int) -> bool:
		"""
		备用的窗口排除检查方法
		结合坐标检查和窗口标题检查
		"""
		try:
			import pyatspi

			desktop = pyatspi.Registry.getDesktop(0)
			if not desktop:
				return False

			# 只检查前几个应用程序，提高性能
			max_apps = min(desktop.childCount, 5)

			for i in range(max_apps):
				try:
					app = desktop.getChildAtIndex(i)
					if not app:
						continue

					# 检查应用程序名称
					app_name = getattr(app, 'name', '') or ''

					# 遍历应用的窗口
					for j in range(min(app.childCount, 3)):  # 只检查前3个窗口
						try:
							window = app.getChildAtIndex(j)
							if not window or not hasattr(window, 'queryComponent'):
								continue

							window_title = getattr(window, 'name', '') or ''

							# 检查是否是录制控制器窗口（通过标题或应用名）
							is_recorder_window = False
							for excluded_title in self.excluded_window_titles:
								if excluded_title in window_title or excluded_title in app_name:
									is_recorder_window = True
									break

							if is_recorder_window:
								component = window.queryComponent()
								if component:
									extents = component.getExtents(pyatspi.DESKTOP_COORDS)
									# 检查坐标是否在窗口范围内
									if (extents.x <= x <= extents.x + extents.width and
										extents.y <= y <= extents.y + extents.height):
										if self.debug:
											print(f"[DEBUG] 🚫 排除录制控制器窗口事件(备用坐标检查): 标题='{window_title}' 应用='{app_name}' 坐标=({x}, {y})", file=sys.stderr)
										return True
						except:
							continue

				except Exception:
					continue

		except Exception as e:
			if self.debug:
				print(f"[DEBUG] 备用窗口过滤检查失败: {e}", file=sys.stderr)

		return False



def start_global_menu_listener():
	"""在主进程启动时启动全局菜单监听器"""
	try:
		# 获取listenHF.py的路径
		current_file = Path(__file__).resolve()
		scripts_dir = current_file.parent.parent
		listener_script = scripts_dir / "listenHF.py"

		if not listener_script.exists():
			print(f"[WARNING] 菜单监听脚本不存在: {listener_script}", file=sys.stderr)
			return None

		# 准备环境变量
		env = os.environ.copy()
		if 'DISPLAY' not in env or not env['DISPLAY']:
			env['DISPLAY'] = ':0'

		# 启动菜单监听器子进程
		process = subprocess.Popen(
			[sys.executable, str(listener_script)],
			env=env,
			stdout=subprocess.DEVNULL,  # 不显示输出，避免干扰
			stderr=subprocess.DEVNULL,
			preexec_fn=os.setsid  # 创建新的进程组
		)

		# 等待一下确保启动成功
		time.sleep(0.5)

		if process.poll() is None:
			print(f"[INFO] 全局菜单监听器已启动，PID: {process.pid}", file=sys.stderr)
			return process
		else:
			print(f"[WARNING] 全局菜单监听器启动失败", file=sys.stderr)
			return None

	except Exception as e:
		print(f"[ERROR] 启动全局菜单监听器失败: {e}", file=sys.stderr)
		return None

if __name__ == "__main__":
	print(f"[INFO] 开始执行全自动录制管理器...\n参数信息为：{args}")

	# 启动全局菜单监听器
	global_menu_process = start_global_menu_listener()

	recorder = AutoRecordingManager(
		debug=args.debug,
		enable_command_listener=args.enable_command_listener,
		storage_path=args.storage_path,
		test_case_id=args.test_case_id,
		testcase_path=args.testcase_path,
		duration=args.duration,
		json_output=args.json_output
	)
	try:
		recorder.start()
		while recorder.running and not recorder.stop_event.is_set():
			time.sleep(0.1)  # 更短的睡眠时间，更快响应停止事件

		# 如果是通过stop_event停止的，调用stop方法
		if recorder.stop_event.is_set():
			recorder.stop()

	except Exception as e:
		print(f"[ERROR] 录制过程发生错误: {e}", file=sys.stderr)
		recorder.stop()
		sys.exit(1)

	finally:
		# 清理全局菜单监听器
		if global_menu_process and global_menu_process.poll() is None:
			try:
				print(f"[INFO] 正在停止全局菜单监听器，PID: {global_menu_process.pid}", file=sys.stderr)
				os.killpg(os.getpgid(global_menu_process.pid), signal.SIGTERM)
				global_menu_process.wait(timeout=3)
				print(f"[INFO] ✅ 全局菜单监听器已停止", file=sys.stderr)
			except Exception as e:
				print(f"[WARNING] 停止全局菜单监听器失败: {e}", file=sys.stderr)
				try:
					os.killpg(os.getpgid(global_menu_process.pid), signal.SIGKILL)
					global_menu_process.wait()
				except:
					pass

