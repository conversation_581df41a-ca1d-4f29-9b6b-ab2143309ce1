[INFO] X11环境检测成功，已加载X11相关库
[INFO] UNI模块从路径加载成功: /home/<USER>/kylin-robot-ide/scripts
[INFO] UNI模块从路径加载成功: scripts
[INFO] 高亮显示模块加载成功
[INFO] Locator生成器加载成功
[2025-08-04 23:28:01]  [UNI] 初始化完成，显示服务器: x11
[2025-08-04 23:28:01]  [INFO] UNI模块初始化成功
[2025-08-04 23:28:01]  [ERROR] 菜单监听脚本不存在: listenHF.py
[2025-08-04 23:28:01]  [INFO] 控件分析器已准备就绪
[2025-08-04 23:28:01]  [INFO] 高亮渲染器已准备就绪
[2025-08-04 23:28:01]  [INFO] 命令监听器已启动
[2025-08-04 23:28:01]  [ERROR] 菜单监听脚本不存在: listenHF.py
[2025-08-04 23:28:01]  [WARNING] 菜单监听器启动失败
[2025-08-04 23:28:01]  [INFO] 悬停检测器已启动
[2025-08-04 23:28:03]  [INFO] 悬停超时触发: 坐标=(20, 801)
[2025-08-04 23:28:04]  [INFO] 开始控件识别...
[2025-08-04 23:28:04]  [UNI] 开始查找坐标(20, 801)处的控件
[2025-08-04 23:28:04]  监听文件无内容，非菜单控件： Expecting value: line 1 column 1 (char 0)
[2025-08-04 23:28:04]  [DEBUG] 获取新的桌面对象
[2025-08-04 23:28:04]  [DEBUG] 桌面对象获取完成，应用程序数量: 50
[2025-08-04 23:28:04]  [DEBUG] 已触发桌面刷新，应用数: 50
[2025-08-04 23:28:04]  [UNI] 获取到活动窗口: [frame | UKUI Panel], 进程ID: 2837
[2025-08-04 23:28:04]  [DEBUG] 使用缓存的桌面对象
[2025-08-04 23:28:04]  [DEBUG] 🚨 开始警告窗口专项检测，坐标: (20, 801)
[2025-08-04 23:28:04]  [DEBUG] 查找X11窗口对应的AT-SPI控件，窗口类名: ukui-panel.ukui-panel
[2025-08-04 23:28:04]  [DEBUG] 使用缓存的桌面对象
[2025-08-04 23:28:04]  [DEBUG] 当前桌面应用程序数量: 50
[2025-08-04 23:28:04]  [DEBUG] 目标窗口类名: 'ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG] === 开始遍历所有AT-SPI应用程序 ===
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='panel-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ✅ 标识符匹配(panel): 得分=95
[2025-08-04 23:28:04]  [DEBUG] 应用程序[0]: 'panel-daemon' -> 匹配得分: 95
[2025-08-04 23:28:04]  [DEBUG] ✅ 找到匹配应用程序: panel-daemon (匹配得分: 95)
[2025-08-04 23:28:04]  [DEBUG] 🔍 高匹配度应用程序 (得分: 95)，开始详细控件查找...
[2025-08-04 23:28:04]  [DEBUG] 🔍 开始在应用程序 'panel-daemon' 中查找坐标 (20, 801) 处的控件...
[2025-08-04 23:28:04]  [DEBUG] ❌ 在高匹配度应用程序 'panel-daemon' 中未找到包含坐标 (20, 801) 的控件
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='sni-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[1]: 'sni-daemon' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='ukui-session' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ✅ 语义相似度匹配: 得分=22
[2025-08-04 23:28:04]  [DEBUG] 应用程序[2]: 'ukui-session' -> 匹配得分: 22
[2025-08-04 23:28:04]  [DEBUG] ✅ 找到匹配应用程序: ukui-session (匹配得分: 22)
[2025-08-04 23:28:04]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-session' (得分: 22 < 90)，不进行详细控件查找
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='sni-xembed-proxy' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[3]: 'sni-xembed-proxy' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='ukuismserver' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ✅ 语义相似度匹配: 得分=9
[2025-08-04 23:28:04]  [DEBUG] 应用程序[4]: 'ukuismserver' -> 匹配得分: 9
[2025-08-04 23:28:04]  [DEBUG] ✅ 找到匹配应用程序: ukuismserver (匹配得分: 9)
[2025-08-04 23:28:04]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukuismserver' (得分: 9 < 90)，不进行详细控件查找
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='桌面' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[5]: '桌面' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='kglobalaccel' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[6]: 'kglobalaccel' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='ukui-panel' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ✅ 反向标识符匹配(ukui-panel): 得分=105
[2025-08-04 23:28:04]  [DEBUG] 应用程序[7]: 'ukui-panel' -> 匹配得分: 105
[2025-08-04 23:28:04]  [DEBUG] ✅ 找到匹配应用程序: ukui-panel (匹配得分: 105)
[2025-08-04 23:28:04]  [DEBUG] 🔍 高匹配度应用程序 (得分: 105)，开始详细控件查找...
[2025-08-04 23:28:04]  [DEBUG] 🔍 开始在应用程序 'ukui-panel' 中查找坐标 (20, 801) 处的控件...
[2025-08-04 23:28:04]  [DEBUG]     找到包含坐标的元素: UKUI Panel (角色: frame, 深度: 0, 子控件数: 1)
[2025-08-04 23:28:04]  [DEBUG]     检查子控件[0] 'unnamed' (frame): 坐标(0, 783) 大小1512x46
[2025-08-04 23:28:04]  [DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(20, 801)
[2025-08-04 23:28:04]  [DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (frame)
[2025-08-04 23:28:04]  [DEBUG]     找到包含坐标的元素: unnamed (角色: frame, 深度: 1, 子控件数: 5)
[2025-08-04 23:28:04]  [DEBUG]     检查子控件[0] 'unnamed' (frame): 坐标(0, 783) 大小93x46
[2025-08-04 23:28:04]  [DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(20, 801)
[2025-08-04 23:28:04]  [DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (frame)
[2025-08-04 23:28:04]  [DEBUG]     找到包含坐标的元素: unnamed (角色: frame, 深度: 2, 子控件数: 1)
[2025-08-04 23:28:04]  [DEBUG]     检查子控件[0] 'unnamed' (frame): 坐标(0, 783) 大小93x46
[2025-08-04 23:28:04]  [DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(20, 801)
[2025-08-04 23:28:04]  [DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (frame)
[2025-08-04 23:28:04]  [DEBUG]     找到包含坐标的元素: unnamed (角色: frame, 深度: 3, 子控件数: 3)
[2025-08-04 23:28:04]  [DEBUG]     检查子控件[0] 'unnamed' (push button): 坐标(0, 783) 大小46x46
[2025-08-04 23:28:04]  [DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(20, 801)
[2025-08-04 23:28:04]  [DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (push button)
[2025-08-04 23:28:04]  [DEBUG]     找到包含坐标的元素: unnamed (角色: push button, 深度: 4, 子控件数: 0)
[2025-08-04 23:28:04]  [DEBUG]     ✅ 找到交互控件: unnamed (角色: push button)
[2025-08-04 23:28:04]  [DEBUG]     🎯 没有找到更好的子控件，返回交互控件: unnamed (角色: push button)
[2025-08-04 23:28:04]  [DEBUG]     🎯 按钮控件超高加分: +150 (角色:push button)
[2025-08-04 23:28:04]  [DEBUG]     检查子控件[1] 'unnamed' (frame): 坐标(46, 798) 大小1x15
[2025-08-04 23:28:04]  [DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(20, 801)
[2025-08-04 23:28:04]  [DEBUG]     检查子控件[2] 'unnamed' (push button): 坐标(47, 783) 大小46x46
[2025-08-04 23:28:04]  [DEBUG]     ❌ 子控件[2] 'unnamed' 不包含目标坐标(20, 801)
[2025-08-04 23:28:04]  [DEBUG]     找到 1 个候选子控件:
[2025-08-04 23:28:04]  [DEBUG]       [0] unnamed (角色: push button, 得分: 1275, 面积: 2116, 分支: 0)
[2025-08-04 23:28:04]  [DEBUG]     🎯 优先选择按钮控件: unnamed (角色: push button)
[2025-08-04 23:28:04]  [DEBUG]     🎯 按钮控件超高加分: +150 (角色:push button)
[2025-08-04 23:28:04]  [DEBUG]     找到 1 个候选子控件:
[2025-08-04 23:28:04]  [DEBUG]       [0] unnamed (角色: push button, 得分: 1265, 面积: 2116, 分支: 0)
[2025-08-04 23:28:04]  [DEBUG]     🎯 优先选择按钮控件: unnamed (角色: push button)
[2025-08-04 23:28:04]  [DEBUG]     🎯 按钮控件超高加分: +150 (角色:push button)
[2025-08-04 23:28:04]  [DEBUG]     检查子控件[1] 'unnamed' (frame): 坐标(93, 783) 大小1040x46
[2025-08-04 23:28:04]  [DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(20, 801)
[2025-08-04 23:28:04]  [DEBUG]     检查子控件[2] 'unnamed' (frame): 坐标(1133, 783) 大小288x46
[2025-08-04 23:28:04]  [DEBUG]     ❌ 子控件[2] 'unnamed' 不包含目标坐标(20, 801)
[2025-08-04 23:28:04]  [DEBUG]     检查子控件[3] 'unnamed' (frame): 坐标(1421, 783) 大小79x46
[2025-08-04 23:28:04]  [DEBUG]     ❌ 子控件[3] 'unnamed' 不包含目标坐标(20, 801)
[2025-08-04 23:28:04]  [DEBUG]     检查子控件[4] 'unnamed' (frame): 坐标(1500, 783) 大小12x46
[2025-08-04 23:28:04]  [DEBUG]     ❌ 子控件[4] 'unnamed' 不包含目标坐标(20, 801)
[2025-08-04 23:28:04]  [DEBUG]     找到 1 个候选子控件:
[2025-08-04 23:28:04]  [DEBUG]       [0] unnamed (角色: push button, 得分: 1255, 面积: 2116, 分支: 0)
[2025-08-04 23:28:04]  [DEBUG]     🎯 优先选择按钮控件: unnamed (角色: push button)
[2025-08-04 23:28:04]  [DEBUG]     🎯 按钮控件超高加分: +150 (角色:push button)
[2025-08-04 23:28:04]  [DEBUG]     找到 1 个候选子控件:
[2025-08-04 23:28:04]  [DEBUG]       [0] unnamed (角色: push button, 得分: 1245, 面积: 2116, 分支: 0)
[2025-08-04 23:28:04]  [DEBUG]     🎯 优先选择按钮控件: unnamed (角色: push button)
[2025-08-04 23:28:04]  [DEBUG] 🔍 从 1 个候选控件中选择最佳控件
[2025-08-04 23:28:04]  [DEBUG]   候选控件: unnamed (角色: push button)
[2025-08-04 23:28:04]  [DEBUG]     向上遍历[0]: unnamed (frame)
[2025-08-04 23:28:04]  [DEBUG]     📋 找到主窗口: unnamed (frame)
[2025-08-04 23:28:04]  [DEBUG]     向上遍历[1]: unnamed (frame)
[2025-08-04 23:28:04]  [DEBUG]     📋 找到主窗口: unnamed (frame)
[2025-08-04 23:28:04]  [DEBUG]     向上遍历[2]: unnamed (frame)
[2025-08-04 23:28:04]  [DEBUG]     📋 找到主窗口: unnamed (frame)
[2025-08-04 23:28:04]  [DEBUG]     向上遍历[3]: UKUI Panel (frame)
[2025-08-04 23:28:04]  [DEBUG]     📋 找到主窗口: UKUI Panel (frame)
[2025-08-04 23:28:04]  [DEBUG]     向上遍历[4]: ukui-panel (application)
[2025-08-04 23:28:04]  [DEBUG]     ⏹️ 到达顶级，停止遍历
[2025-08-04 23:28:04]  [DEBUG]   控件 'unnamed' (push button) 来源窗口类型: main
[2025-08-04 23:28:04]  [DEBUG] ✅ 选择主窗口控件: unnamed (角色: push button)
[2025-08-04 23:28:04]  [DEBUG] ✅ 在应用程序 'ukui-panel' 中找到控件: unnamed
[2025-08-04 23:28:04]  [DEBUG] 🎯 更新最佳匹配控件: unnamed (得分: 105)
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='ukui-kwin' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ✅ 语义相似度匹配: 得分=22
[2025-08-04 23:28:04]  [DEBUG] 应用程序[8]: 'ukui-kwin' -> 匹配得分: 22
[2025-08-04 23:28:04]  [DEBUG] ✅ 找到匹配应用程序: ukui-kwin (匹配得分: 22)
[2025-08-04 23:28:04]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-kwin' (得分: 22 < 90)，不进行详细控件查找
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='ukui-watermark' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ✅ 语义相似度匹配: 得分=22
[2025-08-04 23:28:04]  [DEBUG] 应用程序[9]: 'ukui-watermark' -> 匹配得分: 22
[2025-08-04 23:28:04]  [DEBUG] ✅ 找到匹配应用程序: ukui-watermark (匹配得分: 22)
[2025-08-04 23:28:04]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-watermark' (得分: 22 < 90)，不进行详细控件查找
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='screenmonitorgeneral' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[10]: 'screenMonitorGeneral' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='用户手册' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[11]: '用户手册' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='sogou-qimpanel-watchdog' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ✅ 反向标识符匹配(panel): 得分=95
[2025-08-04 23:28:04]  [DEBUG] 应用程序[12]: 'sogou-qimpanel-watchdog' -> 匹配得分: 95
[2025-08-04 23:28:04]  [DEBUG] ✅ 找到匹配应用程序: sogou-qimpanel-watchdog (匹配得分: 95)
[2025-08-04 23:28:04]  [DEBUG] 🔍 高匹配度应用程序 (得分: 95)，开始详细控件查找...
[2025-08-04 23:28:04]  [DEBUG] 🔍 开始在应用程序 'sogou-qimpanel-watchdog' 中查找坐标 (20, 801) 处的控件...
[2025-08-04 23:28:04]  [DEBUG] ❌ 在高匹配度应用程序 'sogou-qimpanel-watchdog' 中未找到包含坐标 (20, 801) 的控件
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='ukui-upower' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ✅ 语义相似度匹配: 得分=22
[2025-08-04 23:28:04]  [DEBUG] 应用程序[13]: 'ukui-upower' -> 匹配得分: 22
[2025-08-04 23:28:04]  [DEBUG] ✅ 找到匹配应用程序: ukui-upower (匹配得分: 22)
[2025-08-04 23:28:04]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-upower' (得分: 22 < 90)，不进行详细控件查找
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='vdclient' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[14]: 'vdclient' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='kscreen_backend_launcher' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[15]: 'kscreen_backend_launcher' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='secriskbox' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[16]: 'secRiskBox' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='ukui-power-manager-tray' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ✅ 语义相似度匹配: 得分=11
[2025-08-04 23:28:04]  [DEBUG] 应用程序[17]: 'ukui-power-manager-tray' -> 匹配得分: 11
[2025-08-04 23:28:04]  [DEBUG] ✅ 找到匹配应用程序: ukui-power-manager-tray (匹配得分: 11)
[2025-08-04 23:28:04]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-power-manager-tray' (得分: 11 < 90)，不进行详细控件查找
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='ukui-sidebar' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ✅ 语义相似度匹配: 得分=22
[2025-08-04 23:28:04]  [DEBUG] 应用程序[18]: 'ukui-sidebar' -> 匹配得分: 22
[2025-08-04 23:28:04]  [DEBUG] ✅ 找到匹配应用程序: ukui-sidebar (匹配得分: 22)
[2025-08-04 23:28:04]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-sidebar' (得分: 22 < 90)，不进行详细控件查找
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='ukui-notifications' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ✅ 语义相似度匹配: 得分=22
[2025-08-04 23:28:04]  [DEBUG] 应用程序[19]: 'ukui-notifications' -> 匹配得分: 22
[2025-08-04 23:28:04]  [DEBUG] ✅ 找到匹配应用程序: ukui-notifications (匹配得分: 22)
[2025-08-04 23:28:04]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-notifications' (得分: 22 < 90)，不进行详细控件查找
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[20]: 'NotifySend' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ✅ 语义相似度匹配: 得分=14
[2025-08-04 23:28:04]  [DEBUG] 应用程序[21]: 'ukui-settings-daemon' -> 匹配得分: 14
[2025-08-04 23:28:04]  [DEBUG] ✅ 找到匹配应用程序: ukui-settings-daemon (匹配得分: 14)
[2025-08-04 23:28:04]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-settings-daemon' (得分: 14 < 90)，不进行详细控件查找
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ✅ 语义相似度匹配: 得分=14
[2025-08-04 23:28:04]  [DEBUG] 应用程序[22]: 'ukui-settings-daemon' -> 匹配得分: 14
[2025-08-04 23:28:04]  [DEBUG] ✅ 找到匹配应用程序: ukui-settings-daemon (匹配得分: 14)
[2025-08-04 23:28:04]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-settings-daemon' (得分: 14 < 90)，不进行详细控件查找
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[23]: 'NotifySend' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='ukui-powermanagement' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ✅ 语义相似度匹配: 得分=22
[2025-08-04 23:28:04]  [DEBUG] 应用程序[24]: 'ukui-powermanagement' -> 匹配得分: 22
[2025-08-04 23:28:04]  [DEBUG] ✅ 找到匹配应用程序: ukui-powermanagement (匹配得分: 22)
[2025-08-04 23:28:04]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-powermanagement' (得分: 22 < 90)，不进行详细控件查找
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='ukui-volume-control-applet-qt' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ✅ 语义相似度匹配: 得分=9
[2025-08-04 23:28:04]  [DEBUG] 应用程序[25]: 'ukui-volume-control-applet-qt' -> 匹配得分: 9
[2025-08-04 23:28:04]  [DEBUG] ✅ 找到匹配应用程序: ukui-volume-control-applet-qt (匹配得分: 9)
[2025-08-04 23:28:04]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-volume-control-applet-qt' (得分: 9 < 90)，不进行详细控件查找
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='vino-server' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[26]: 'vino-server' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='麒麟id' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[27]: '麒麟ID' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='kylin-device-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[28]: 'kylin-device-daemon' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='kylin-process-manager' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[29]: 'kylin-process-manager' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='ukui-bluetooth' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ✅ 语义相似度匹配: 得分=22
[2025-08-04 23:28:04]  [DEBUG] 应用程序[30]: 'ukui-bluetooth' -> 匹配得分: 22
[2025-08-04 23:28:04]  [DEBUG] ✅ 找到匹配应用程序: ukui-bluetooth (匹配得分: 22)
[2025-08-04 23:28:04]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-bluetooth' (得分: 22 < 90)，不进行详细控件查找
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='polkit-ukui-authentication-agent-1' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ✅ 语义相似度匹配: 得分=11
[2025-08-04 23:28:04]  [DEBUG] 应用程序[31]: 'polkit-ukui-authentication-agent-1' -> 匹配得分: 11
[2025-08-04 23:28:04]  [DEBUG] ✅ 找到匹配应用程序: polkit-ukui-authentication-agent-1 (匹配得分: 11)
[2025-08-04 23:28:04]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'polkit-ukui-authentication-agent-1' (得分: 11 < 90)，不进行详细控件查找
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='天气' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[32]: '天气' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='kylin-vpn' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[33]: 'kylin-vpn' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='kylin note' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[34]: 'Kylin Note' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[35]: 'kylin-nm' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[36]: 'kylin-nm' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='kylin-printer-applet' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ✅ 语义相似度匹配: 得分=1
[2025-08-04 23:28:04]  [DEBUG] 应用程序[37]: 'kylin-printer-applet' -> 匹配得分: 1
[2025-08-04 23:28:04]  [DEBUG] ✅ 找到匹配应用程序: kylin-printer-applet (匹配得分: 1)
[2025-08-04 23:28:04]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'kylin-printer-applet' (得分: 1 < 90)，不进行详细控件查找
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='ukui-menu' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ✅ 语义相似度匹配: 得分=22
[2025-08-04 23:28:04]  [DEBUG] 应用程序[38]: 'ukui-menu' -> 匹配得分: 22
[2025-08-04 23:28:04]  [DEBUG] ✅ 找到匹配应用程序: ukui-menu (匹配得分: 22)
[2025-08-04 23:28:04]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-menu' (得分: 22 < 90)，不进行详细控件查找
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='ukui-search-service-dir-manager' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ✅ 语义相似度匹配: 得分=8
[2025-08-04 23:28:04]  [DEBUG] 应用程序[39]: 'ukui-search-service-dir-manager' -> 匹配得分: 8
[2025-08-04 23:28:04]  [DEBUG] ✅ 找到匹配应用程序: ukui-search-service-dir-manager (匹配得分: 8)
[2025-08-04 23:28:04]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-service-dir-manager' (得分: 8 < 90)，不进行详细控件查找
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='ukui-search-app-data-service' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ✅ 语义相似度匹配: 得分=8
[2025-08-04 23:28:04]  [DEBUG] 应用程序[40]: 'ukui-search-app-data-service' -> 匹配得分: 8
[2025-08-04 23:28:04]  [DEBUG] ✅ 找到匹配应用程序: ukui-search-app-data-service (匹配得分: 8)
[2025-08-04 23:28:04]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-app-data-service' (得分: 8 < 90)，不进行详细控件查找
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='ukui-search-service' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ✅ 语义相似度匹配: 得分=14
[2025-08-04 23:28:04]  [DEBUG] 应用程序[41]: 'ukui-search-service' -> 匹配得分: 14
[2025-08-04 23:28:04]  [DEBUG] ✅ 找到匹配应用程序: ukui-search-service (匹配得分: 14)
[2025-08-04 23:28:04]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-service' (得分: 14 < 90)，不进行详细控件查找
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='prlcc' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[42]: 'prlcc' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='ukui-search' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ✅ 语义相似度匹配: 得分=22
[2025-08-04 23:28:04]  [DEBUG] 应用程序[43]: 'ukui-search' -> 匹配得分: 22
[2025-08-04 23:28:04]  [DEBUG] ✅ 找到匹配应用程序: ukui-search (匹配得分: 22)
[2025-08-04 23:28:04]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search' (得分: 22 < 90)，不进行详细控件查找
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='sogouimeservice' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[44]: 'sogouImeService' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='ksc-defender' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[45]: 'ksc-defender' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='xdg-desktop-portal-gtk' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[46]: 'xdg-desktop-portal-gtk' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='mate-terminal' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[47]: 'mate-terminal' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='accerciser' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[48]: 'accerciser' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG]   智能匹配检查: 应用程序='文件管理器' vs 窗口类名='ukui-panel.ukui-panel'
[2025-08-04 23:28:04]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:04]  [DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[2025-08-04 23:28:04]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:04]  [DEBUG] 应用程序[49]: '文件管理器' -> 匹配得分: 0
[2025-08-04 23:28:04]  [DEBUG] === 第一轮精确匹配完成 ===
[2025-08-04 23:28:04]  [DEBUG] 所有应用程序: [0] 'panel-daemon', [1] 'sni-daemon', [2] 'ukui-session', [3] 'sni-xembed-proxy', [4] 'ukuismserver'...
[2025-08-04 23:28:04]  [DEBUG] ✅ 找到高精度匹配控件: 得分=105
[2025-08-04 23:28:04]  [DEBUG] X11层级检测成功找到控件: unnamed
[2025-08-04 23:28:04]  [INFO] 使用X11层级检测在坐标 (20, 801) 处找到最顶层控件: unnamed
[2025-08-04 23:28:04]  [UNI] 查找控件结果: [push button | ]
[2025-08-04 23:28:04]  [UNI] 控件ParentPath: [2, 0, 0, 0, 0]
[2025-08-04 23:28:04]  [UNI] ParentPath第一个节点索引: 2
[2025-08-04 23:28:04]  [UNI] 找到应用程序对象: ukui-panel
[2025-08-04 23:28:04]  [UNI] ParentPath第一个节点对象: UKUI Panel (角色: frame)
[2025-08-04 23:28:04]  [UNI] 更新WindowRoleName为ParentPath第一个节点角色: frame
[2025-08-04 23:28:04]  [UNI] 提取到控件信息带窗口: {'Name': 'N/A', 'ID': -1, 'ProcessID': 2837, 'Rolename': 'push button', 'Description': 'N/A', 'Index_in_parent': 'N/A', 'ChildrenCount': 0, 'ProcessName': 'ukui-panel', 'Coords': {'x': 0, 'y': 783, 'width': 46, 'height': 46}, 'Text': 'Not available: ', 'Actions': ['Press', 'SetFocus'], 'States': ['enabled', 'focusable', 'sensitive', 'showing', 'visible'], 'ParentPath': [2, 0, 0, 0, 0], 'ParentCount': 5, 'Key': 'NNA-DNA-P20000', 'RecordPosition': (20, 801), 'WindowRoleName': 'frame', 'WindowChildCount': 1}
[2025-08-04 23:28:04]  [UNI] 控件ParentPath: [2, 0, 0, 0, 0]
[2025-08-04 23:28:04]  [UNI] ParentPath第一个节点索引: 2
[2025-08-04 23:28:04]  [UNI] 找到应用程序对象: ukui-panel
[2025-08-04 23:28:04]  [UNI] ParentPath第一个节点对象: UKUI Panel (角色: frame)
[2025-08-04 23:28:04]  [UNI] 控件实际所属窗口: UKUI Panel
[2025-08-04 23:28:04]  [UNI] 提取到控件信息带窗口: {'Name': 'N/A', 'ID': -1, 'ProcessID': 2837, 'Rolename': 'push button', 'Description': 'N/A', 'Index_in_parent': 'N/A', 'ChildrenCount': 0, 'ProcessName': 'ukui-panel', 'Coords': {'x': 0, 'y': 783, 'width': 46, 'height': 46}, 'Text': 'Not available: ', 'Actions': ['Press', 'SetFocus'], 'States': ['enabled', 'focusable', 'sensitive', 'showing', 'visible'], 'ParentPath': [2, 0, 0, 0, 0], 'ParentCount': 5, 'Key': 'NNA-DNA-P20000', 'RecordPosition': (20, 801), 'WindowRoleName': 'frame', 'WindowChildCount': 1, 'WindowName': 'UKUI Panel'}
[2025-08-04 23:28:04]  [UNI] 控件名称生成为: 按钮_(0,783)
[2025-08-04 23:28:04]  [UNI] 控件信息验证通过
[2025-08-04 23:28:04]  ⚡ [FAST] 控件识别快速: 0.176秒 | 方法: analyze_widget_at_with_new_app_detection | 坐标: (20, 801)
[2025-08-04 23:28:04]  [INFO] 控件识别完成，耗时: 0.176秒
[2025-08-04 23:28:04]  
============================================================
[2025-08-04 23:28:04]  [SUCCESS] 成功识别控件信息:
[2025-08-04 23:28:04]    名称: 按钮_(0,783)
[2025-08-04 23:28:04]    类型: Unknown
[2025-08-04 23:28:04]    进程: ukui-panel
[2025-08-04 23:28:04]    位置: x=0, y=783, 宽=46, 高=46
[2025-08-04 23:28:04]  ============================================================

[2025-08-04 23:28:04]  [INFO] 🎯 悬停识别成功并缓存: 按钮_(0,783) (进程: ukui-panel)
[2025-08-04 23:28:04]  [INFO] 记录拖动起始点: (17, 801) 按钮=left
[2025-08-04 23:28:04]  [INFO] 鼠标释放检查: 起始(17, 801) 结束(17, 801) 距离=0.0px 阈值=10px
[2025-08-04 23:28:04]  [INFO] 距离不足，不是拖动: 0.0px < 10px
[2025-08-04 23:28:04]  
************************************************************
[2025-08-04 23:28:04]  [CLICK] 点击控件信息:
[2025-08-04 23:28:04]    名称: 按钮_(0,783)
[2025-08-04 23:28:04]    位置: (17, 801)
[2025-08-04 23:28:04]  ************************************************************

[2025-08-04 23:28:04]  [EVENT] 记录click事件: 位置=(17, 801), 控件=按钮_(0,783)
[2025-08-04 23:28:06]  [INFO] 悬停超时触发: 坐标=(189, 385)
[2025-08-04 23:28:06]  [INFO] 开始控件识别...
[2025-08-04 23:28:06]  [UNI] 开始查找坐标(189, 385)处的控件
[2025-08-04 23:28:06]  监听文件无内容，非菜单控件： Expecting value: line 1 column 1 (char 0)
[2025-08-04 23:28:06]  [DEBUG] 获取新的桌面对象
[2025-08-04 23:28:06]  [DEBUG] 桌面对象获取完成，应用程序数量: 50
[2025-08-04 23:28:06]  [DEBUG] 已触发桌面刷新，应用数: 50
[2025-08-04 23:28:06]  [UNI] 获取到活动窗口: [frame | kylin@kylin-pc: ~/kylin-robot-ide/scripts/recorder], 进程ID: 129851
[2025-08-04 23:28:06]  [DEBUG] 使用缓存的桌面对象
[2025-08-04 23:28:06]  [DEBUG] 🚨 开始警告窗口专项检测，坐标: (189, 385)
[2025-08-04 23:28:06]  [DEBUG] 查找X11窗口对应的AT-SPI控件，窗口类名: ukui-menu.ukui-menu
[2025-08-04 23:28:06]  [DEBUG] 使用缓存的桌面对象
[2025-08-04 23:28:06]  [DEBUG] 当前桌面应用程序数量: 50
[2025-08-04 23:28:06]  [DEBUG] 目标窗口类名: 'ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG] 应用程序数量未变化，尝试强制刷新AT-SPI
[2025-08-04 23:28:06]  [DEBUG] 强制刷新后应用程序数量: 50
[2025-08-04 23:28:06]  [DEBUG] === 开始遍历所有AT-SPI应用程序 ===
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='panel-daemon' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[0]: 'panel-daemon' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='sni-daemon' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[1]: 'sni-daemon' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='ukui-session' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ✅ 语义相似度匹配: 得分=22
[2025-08-04 23:28:06]  [DEBUG] 应用程序[2]: 'ukui-session' -> 匹配得分: 22
[2025-08-04 23:28:06]  [DEBUG] ✅ 找到匹配应用程序: ukui-session (匹配得分: 22)
[2025-08-04 23:28:06]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-session' (得分: 22 < 90)，不进行详细控件查找
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='sni-xembed-proxy' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[3]: 'sni-xembed-proxy' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='ukuismserver' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ✅ 语义相似度匹配: 得分=9
[2025-08-04 23:28:06]  [DEBUG] 应用程序[4]: 'ukuismserver' -> 匹配得分: 9
[2025-08-04 23:28:06]  [DEBUG] ✅ 找到匹配应用程序: ukuismserver (匹配得分: 9)
[2025-08-04 23:28:06]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukuismserver' (得分: 9 < 90)，不进行详细控件查找
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='桌面' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[5]: '桌面' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='kglobalaccel' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[6]: 'kglobalaccel' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='ukui-panel' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ✅ 语义相似度匹配: 得分=22
[2025-08-04 23:28:06]  [DEBUG] 应用程序[7]: 'ukui-panel' -> 匹配得分: 22
[2025-08-04 23:28:06]  [DEBUG] ✅ 找到匹配应用程序: ukui-panel (匹配得分: 22)
[2025-08-04 23:28:06]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-panel' (得分: 22 < 90)，不进行详细控件查找
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='ukui-kwin' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ✅ 语义相似度匹配: 得分=22
[2025-08-04 23:28:06]  [DEBUG] 应用程序[8]: 'ukui-kwin' -> 匹配得分: 22
[2025-08-04 23:28:06]  [DEBUG] ✅ 找到匹配应用程序: ukui-kwin (匹配得分: 22)
[2025-08-04 23:28:06]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-kwin' (得分: 22 < 90)，不进行详细控件查找
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='ukui-watermark' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ✅ 语义相似度匹配: 得分=22
[2025-08-04 23:28:06]  [DEBUG] 应用程序[9]: 'ukui-watermark' -> 匹配得分: 22
[2025-08-04 23:28:06]  [DEBUG] ✅ 找到匹配应用程序: ukui-watermark (匹配得分: 22)
[2025-08-04 23:28:06]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-watermark' (得分: 22 < 90)，不进行详细控件查找
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='screenmonitorgeneral' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[10]: 'screenMonitorGeneral' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='用户手册' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[11]: '用户手册' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='sogou-qimpanel-watchdog' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[12]: 'sogou-qimpanel-watchdog' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='ukui-upower' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ✅ 语义相似度匹配: 得分=22
[2025-08-04 23:28:06]  [DEBUG] 应用程序[13]: 'ukui-upower' -> 匹配得分: 22
[2025-08-04 23:28:06]  [DEBUG] ✅ 找到匹配应用程序: ukui-upower (匹配得分: 22)
[2025-08-04 23:28:06]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-upower' (得分: 22 < 90)，不进行详细控件查找
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='vdclient' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[14]: 'vdclient' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='kscreen_backend_launcher' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[15]: 'kscreen_backend_launcher' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='secriskbox' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[16]: 'secRiskBox' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='ukui-power-manager-tray' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ✅ 语义相似度匹配: 得分=11
[2025-08-04 23:28:06]  [DEBUG] 应用程序[17]: 'ukui-power-manager-tray' -> 匹配得分: 11
[2025-08-04 23:28:06]  [DEBUG] ✅ 找到匹配应用程序: ukui-power-manager-tray (匹配得分: 11)
[2025-08-04 23:28:06]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-power-manager-tray' (得分: 11 < 90)，不进行详细控件查找
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='ukui-sidebar' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ✅ 语义相似度匹配: 得分=22
[2025-08-04 23:28:06]  [DEBUG] 应用程序[18]: 'ukui-sidebar' -> 匹配得分: 22
[2025-08-04 23:28:06]  [DEBUG] ✅ 找到匹配应用程序: ukui-sidebar (匹配得分: 22)
[2025-08-04 23:28:06]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-sidebar' (得分: 22 < 90)，不进行详细控件查找
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='ukui-notifications' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ✅ 语义相似度匹配: 得分=22
[2025-08-04 23:28:06]  [DEBUG] 应用程序[19]: 'ukui-notifications' -> 匹配得分: 22
[2025-08-04 23:28:06]  [DEBUG] ✅ 找到匹配应用程序: ukui-notifications (匹配得分: 22)
[2025-08-04 23:28:06]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-notifications' (得分: 22 < 90)，不进行详细控件查找
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[20]: 'NotifySend' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ✅ 语义相似度匹配: 得分=14
[2025-08-04 23:28:06]  [DEBUG] 应用程序[21]: 'ukui-settings-daemon' -> 匹配得分: 14
[2025-08-04 23:28:06]  [DEBUG] ✅ 找到匹配应用程序: ukui-settings-daemon (匹配得分: 14)
[2025-08-04 23:28:06]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-settings-daemon' (得分: 14 < 90)，不进行详细控件查找
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ✅ 语义相似度匹配: 得分=14
[2025-08-04 23:28:06]  [DEBUG] 应用程序[22]: 'ukui-settings-daemon' -> 匹配得分: 14
[2025-08-04 23:28:06]  [DEBUG] ✅ 找到匹配应用程序: ukui-settings-daemon (匹配得分: 14)
[2025-08-04 23:28:06]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-settings-daemon' (得分: 14 < 90)，不进行详细控件查找
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[23]: 'NotifySend' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='ukui-powermanagement' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ✅ 语义相似度匹配: 得分=22
[2025-08-04 23:28:06]  [DEBUG] 应用程序[24]: 'ukui-powermanagement' -> 匹配得分: 22
[2025-08-04 23:28:06]  [DEBUG] ✅ 找到匹配应用程序: ukui-powermanagement (匹配得分: 22)
[2025-08-04 23:28:06]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-powermanagement' (得分: 22 < 90)，不进行详细控件查找
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='ukui-volume-control-applet-qt' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ✅ 语义相似度匹配: 得分=8
[2025-08-04 23:28:06]  [DEBUG] 应用程序[25]: 'ukui-volume-control-applet-qt' -> 匹配得分: 8
[2025-08-04 23:28:06]  [DEBUG] ✅ 找到匹配应用程序: ukui-volume-control-applet-qt (匹配得分: 8)
[2025-08-04 23:28:06]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-volume-control-applet-qt' (得分: 8 < 90)，不进行详细控件查找
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='vino-server' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[26]: 'vino-server' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='麒麟id' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[27]: '麒麟ID' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='kylin-device-daemon' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[28]: 'kylin-device-daemon' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='kylin-process-manager' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[29]: 'kylin-process-manager' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='ukui-bluetooth' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ✅ 语义相似度匹配: 得分=22
[2025-08-04 23:28:06]  [DEBUG] 应用程序[30]: 'ukui-bluetooth' -> 匹配得分: 22
[2025-08-04 23:28:06]  [DEBUG] ✅ 找到匹配应用程序: ukui-bluetooth (匹配得分: 22)
[2025-08-04 23:28:06]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-bluetooth' (得分: 22 < 90)，不进行详细控件查找
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='polkit-ukui-authentication-agent-1' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ✅ 语义相似度匹配: 得分=11
[2025-08-04 23:28:06]  [DEBUG] 应用程序[31]: 'polkit-ukui-authentication-agent-1' -> 匹配得分: 11
[2025-08-04 23:28:06]  [DEBUG] ✅ 找到匹配应用程序: polkit-ukui-authentication-agent-1 (匹配得分: 11)
[2025-08-04 23:28:06]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'polkit-ukui-authentication-agent-1' (得分: 11 < 90)，不进行详细控件查找
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='天气' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[32]: '天气' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='kylin-vpn' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[33]: 'kylin-vpn' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='kylin note' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[34]: 'Kylin Note' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[35]: 'kylin-nm' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[36]: 'kylin-nm' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='kylin-printer-applet' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[37]: 'kylin-printer-applet' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='ukui-menu' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ✅ 反向标识符匹配(ukui-menu): 得分=103
[2025-08-04 23:28:06]  [DEBUG] 应用程序[38]: 'ukui-menu' -> 匹配得分: 103
[2025-08-04 23:28:06]  [DEBUG] ✅ 找到匹配应用程序: ukui-menu (匹配得分: 103)
[2025-08-04 23:28:06]  [DEBUG] 🔍 高匹配度应用程序 (得分: 103)，开始详细控件查找...
[2025-08-04 23:28:06]  [DEBUG] 🔍 开始在应用程序 'ukui-menu' 中查找坐标 (189, 385) 处的控件...
[2025-08-04 23:28:06]  [DEBUG]     找到包含坐标的元素: unnamed (角色: frame, 深度: 0, 子控件数: 1)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[0] 'unnamed' (frame): 坐标(8, 181) 大小382x594
[2025-08-04 23:28:06]  [DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (frame)
[2025-08-04 23:28:06]  [DEBUG]     找到包含坐标的元素: unnamed (角色: frame, 深度: 1, 子控件数: 3)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[0] 'unnamed' (filler): 坐标(9, 181) 大小320x594
[2025-08-04 23:28:06]  [DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (filler)
[2025-08-04 23:28:06]  [DEBUG]     找到包含坐标的元素: unnamed (角色: filler, 深度: 2, 子控件数: 2)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[0] 'unnamed' (filler): 坐标(9, 181) 大小320x54
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[0] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[1] 'unnamed' (filler): 坐标(9, 235) 大小320x540
[2025-08-04 23:28:06]  [DEBUG]     ✅ 子控件[1] 'unnamed' 包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     🔍 开始递归搜索子控件[1] 'unnamed' (filler)
[2025-08-04 23:28:06]  [DEBUG]     找到包含坐标的元素: unnamed (角色: filler, 深度: 3, 子控件数: 8)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[0] 'unnamed' (filler): 坐标(9, 235) 大小320x536
[2025-08-04 23:28:06]  [DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (filler)
[2025-08-04 23:28:06]  [DEBUG]     找到包含坐标的元素: unnamed (角色: filler, 深度: 4, 子控件数: 1)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[0] 'unnamed' (list): 坐标(15, 235) 大小314x530
[2025-08-04 23:28:06]  [DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (list)
[2025-08-04 23:28:06]  [DEBUG]     找到包含坐标的元素: unnamed (角色: list, 深度: 5, 子控件数: 53)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[0] 'unnamed' (list item): 坐标(15, 235) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[0] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[1] 'unnamed' (list item): 坐标(15, 279) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[2] 'unnamed' (list item): 坐标(15, 323) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[2] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[3] 'unnamed' (list item): 坐标(15, 367) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ✅ 子控件[3] 'unnamed' 包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     🔍 开始递归搜索子控件[3] 'unnamed' (list item)
[2025-08-04 23:28:06]  [DEBUG]     找到包含坐标的元素: unnamed (角色: list item, 深度: 6, 子控件数: 0)
[2025-08-04 23:28:06]  [DEBUG]     ✅ 交互控件加分: +50 (角色:list item)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[4] 'unnamed' (list item): 坐标(15, 411) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[4] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[5] 'unnamed' (list item): 坐标(15, 455) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[5] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[6] 'unnamed' (list item): 坐标(15, 499) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[6] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[7] 'unnamed' (list item): 坐标(15, 543) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[7] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[8] 'unnamed' (list item): 坐标(15, 587) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[8] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[9] 'unnamed' (list item): 坐标(15, 631) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[9] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[10] 'unnamed' (list item): 坐标(15, 675) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[10] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[11] 'unnamed' (list item): 坐标(15, 719) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[11] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[12] 'unnamed' (list item): 坐标(15, 763) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[12] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[13] 'unnamed' (list item): 坐标(15, 807) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[13] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[14] 'unnamed' (list item): 坐标(15, 851) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[14] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[15] 'unnamed' (list item): 坐标(15, 895) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[15] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[16] 'unnamed' (list item): 坐标(15, 939) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[16] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[17] 'unnamed' (list item): 坐标(15, 983) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[17] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[18] 'unnamed' (list item): 坐标(15, 1027) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[18] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[19] 'unnamed' (list item): 坐标(15, 1071) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[19] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[20] 'unnamed' (list item): 坐标(15, 1115) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[20] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[21] 'unnamed' (list item): 坐标(15, 1159) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[21] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[22] 'unnamed' (list item): 坐标(15, 1203) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[22] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[23] 'unnamed' (list item): 坐标(15, 1247) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[23] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[24] 'unnamed' (list item): 坐标(15, 1291) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[24] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[25] 'unnamed' (list item): 坐标(15, 1335) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[25] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[26] 'unnamed' (list item): 坐标(15, 1379) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[26] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[27] 'unnamed' (list item): 坐标(15, 1423) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[27] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[28] 'unnamed' (list item): 坐标(15, 1467) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[28] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[29] 'unnamed' (list item): 坐标(15, 1511) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[29] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[30] 'unnamed' (list item): 坐标(15, 1555) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[30] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[31] 'unnamed' (list item): 坐标(15, 1599) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[31] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[32] 'unnamed' (list item): 坐标(15, 1643) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[32] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[33] 'unnamed' (list item): 坐标(15, 1687) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[33] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[34] 'unnamed' (list item): 坐标(15, 1731) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[34] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[35] 'unnamed' (list item): 坐标(15, 1775) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[35] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[36] 'unnamed' (list item): 坐标(15, 1819) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[36] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[37] 'unnamed' (list item): 坐标(15, 1863) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[37] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[38] 'unnamed' (list item): 坐标(15, 1907) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[38] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[39] 'unnamed' (list item): 坐标(15, 1951) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[39] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[40] 'unnamed' (list item): 坐标(15, 1995) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[40] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[41] 'unnamed' (list item): 坐标(15, 2039) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[41] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[42] 'unnamed' (list item): 坐标(15, 2083) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[42] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[43] 'unnamed' (list item): 坐标(15, 2127) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[43] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[44] 'unnamed' (list item): 坐标(15, 2171) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[44] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[45] 'unnamed' (list item): 坐标(15, 2215) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[45] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[46] 'unnamed' (list item): 坐标(15, 2259) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[46] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[47] 'unnamed' (list item): 坐标(15, 2303) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[47] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[48] 'unnamed' (list item): 坐标(15, 2347) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[48] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[49] 'unnamed' (list item): 坐标(15, 2391) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[49] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[50] 'unnamed' (list item): 坐标(15, 2435) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[50] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[51] 'unnamed' (list item): 坐标(15, 2479) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[51] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[52] 'unnamed' (list item): 坐标(15, 2523) 大小298x44
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[52] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     找到 1 个候选子控件:
[2025-08-04 23:28:06]  [DEBUG]       [0] unnamed (角色: list item, 得分: 185, 面积: 13112, 分支: 3)
[2025-08-04 23:28:06]  [DEBUG]     🔍 只找到容器控件，继续深入搜索...
[2025-08-04 23:28:06]  [DEBUG]     🔍 深入搜索容器: unnamed (角色: list item)
[2025-08-04 23:28:06]  [DEBUG]       深度6: 开始搜索容器 'unnamed' (list item)，子控件数: 0
[2025-08-04 23:28:06]  [DEBUG]       深度6: 容器搜索完成，未找到交互控件
[2025-08-04 23:28:06]  [DEBUG]     ⚠️ 选择得分最高控件: unnamed (角色: list item)
[2025-08-04 23:28:06]  [DEBUG]     ✅ 交互控件加分: +50 (角色:list item)
[2025-08-04 23:28:06]  [DEBUG]     找到 1 个候选子控件:
[2025-08-04 23:28:06]  [DEBUG]       [0] unnamed (角色: list item, 得分: 175, 面积: 13112, 分支: 0)
[2025-08-04 23:28:06]  [DEBUG]     🔍 只找到容器控件，继续深入搜索...
[2025-08-04 23:28:06]  [DEBUG]     🔍 深入搜索容器: unnamed (角色: list item)
[2025-08-04 23:28:06]  [DEBUG]       深度5: 开始搜索容器 'unnamed' (list item)，子控件数: 0
[2025-08-04 23:28:06]  [DEBUG]       深度5: 容器搜索完成，未找到交互控件
[2025-08-04 23:28:06]  [DEBUG]     ⚠️ 选择得分最高控件: unnamed (角色: list item)
[2025-08-04 23:28:06]  [DEBUG]     ✅ 交互控件加分: +50 (角色:list item)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[1] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[2] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[2] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[3] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[3] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[4] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[4] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[5] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[5] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[6] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[6] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[7] 'unnamed' (filler): 坐标(0, 0) 大小0x0
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[7] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     找到 1 个候选子控件:
[2025-08-04 23:28:06]  [DEBUG]       [0] unnamed (角色: list item, 得分: 165, 面积: 13112, 分支: 0)
[2025-08-04 23:28:06]  [DEBUG]     🔍 只找到容器控件，继续深入搜索...
[2025-08-04 23:28:06]  [DEBUG]     🔍 深入搜索容器: unnamed (角色: list item)
[2025-08-04 23:28:06]  [DEBUG]       深度4: 开始搜索容器 'unnamed' (list item)，子控件数: 0
[2025-08-04 23:28:06]  [DEBUG]       深度4: 容器搜索完成，未找到交互控件
[2025-08-04 23:28:06]  [DEBUG]     ⚠️ 选择得分最高控件: unnamed (角色: list item)
[2025-08-04 23:28:06]  [DEBUG]     ✅ 交互控件加分: +50 (角色:list item)
[2025-08-04 23:28:06]  [DEBUG]     找到 1 个候选子控件:
[2025-08-04 23:28:06]  [DEBUG]       [0] unnamed (角色: list item, 得分: 155, 面积: 13112, 分支: 1)
[2025-08-04 23:28:06]  [DEBUG]     🔍 只找到容器控件，继续深入搜索...
[2025-08-04 23:28:06]  [DEBUG]     🔍 深入搜索容器: unnamed (角色: list item)
[2025-08-04 23:28:06]  [DEBUG]       深度3: 开始搜索容器 'unnamed' (list item)，子控件数: 0
[2025-08-04 23:28:06]  [DEBUG]       深度3: 容器搜索完成，未找到交互控件
[2025-08-04 23:28:06]  [DEBUG]     ⚠️ 选择得分最高控件: unnamed (角色: list item)
[2025-08-04 23:28:06]  [DEBUG]     ✅ 交互控件加分: +50 (角色:list item)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[1] 'unnamed' (frame): 坐标(330, 181) 大小1x593
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     检查子控件[2] 'unnamed' (filler): 坐标(332, 181) 大小55x594
[2025-08-04 23:28:06]  [DEBUG]     ❌ 子控件[2] 'unnamed' 不包含目标坐标(189, 385)
[2025-08-04 23:28:06]  [DEBUG]     找到 1 个候选子控件:
[2025-08-04 23:28:06]  [DEBUG]       [0] unnamed (角色: list item, 得分: 145, 面积: 13112, 分支: 0)
[2025-08-04 23:28:06]  [DEBUG]     🔍 只找到容器控件，继续深入搜索...
[2025-08-04 23:28:06]  [DEBUG]     🔍 深入搜索容器: unnamed (角色: list item)
[2025-08-04 23:28:06]  [DEBUG]       深度2: 开始搜索容器 'unnamed' (list item)，子控件数: 0
[2025-08-04 23:28:06]  [DEBUG]       深度2: 容器搜索完成，未找到交互控件
[2025-08-04 23:28:06]  [DEBUG]     ⚠️ 选择得分最高控件: unnamed (角色: list item)
[2025-08-04 23:28:06]  [DEBUG]     ✅ 交互控件加分: +50 (角色:list item)
[2025-08-04 23:28:06]  [DEBUG]     找到 1 个候选子控件:
[2025-08-04 23:28:06]  [DEBUG]       [0] unnamed (角色: list item, 得分: 135, 面积: 13112, 分支: 0)
[2025-08-04 23:28:06]  [DEBUG]     🔍 只找到容器控件，继续深入搜索...
[2025-08-04 23:28:06]  [DEBUG]     🔍 深入搜索容器: unnamed (角色: list item)
[2025-08-04 23:28:06]  [DEBUG]       深度1: 开始搜索容器 'unnamed' (list item)，子控件数: 0
[2025-08-04 23:28:06]  [DEBUG]       深度1: 容器搜索完成，未找到交互控件
[2025-08-04 23:28:06]  [DEBUG]     ⚠️ 选择得分最高控件: unnamed (角色: list item)
[2025-08-04 23:28:06]  [DEBUG] 🔍 从 1 个候选控件中选择最佳控件
[2025-08-04 23:28:06]  [DEBUG]   候选控件: unnamed (角色: list item)
[2025-08-04 23:28:06]  [DEBUG] ⚠️ 选择容器控件: unnamed (角色: list item)
[2025-08-04 23:28:06]  [DEBUG] ✅ 在应用程序 'ukui-menu' 中找到控件: unnamed
[2025-08-04 23:28:06]  [DEBUG] 🎯 更新最佳匹配控件: unnamed (得分: 103)
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='ukui-search-service-dir-manager' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ✅ 语义相似度匹配: 得分=8
[2025-08-04 23:28:06]  [DEBUG] 应用程序[39]: 'ukui-search-service-dir-manager' -> 匹配得分: 8
[2025-08-04 23:28:06]  [DEBUG] ✅ 找到匹配应用程序: ukui-search-service-dir-manager (匹配得分: 8)
[2025-08-04 23:28:06]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-service-dir-manager' (得分: 8 < 90)，不进行详细控件查找
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='ukui-search-app-data-service' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ✅ 语义相似度匹配: 得分=8
[2025-08-04 23:28:06]  [DEBUG] 应用程序[40]: 'ukui-search-app-data-service' -> 匹配得分: 8
[2025-08-04 23:28:06]  [DEBUG] ✅ 找到匹配应用程序: ukui-search-app-data-service (匹配得分: 8)
[2025-08-04 23:28:06]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-app-data-service' (得分: 8 < 90)，不进行详细控件查找
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='ukui-search-service' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ✅ 语义相似度匹配: 得分=14
[2025-08-04 23:28:06]  [DEBUG] 应用程序[41]: 'ukui-search-service' -> 匹配得分: 14
[2025-08-04 23:28:06]  [DEBUG] ✅ 找到匹配应用程序: ukui-search-service (匹配得分: 14)
[2025-08-04 23:28:06]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-service' (得分: 14 < 90)，不进行详细控件查找
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='prlcc' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[42]: 'prlcc' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='ukui-search' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ✅ 语义相似度匹配: 得分=22
[2025-08-04 23:28:06]  [DEBUG] 应用程序[43]: 'ukui-search' -> 匹配得分: 22
[2025-08-04 23:28:06]  [DEBUG] ✅ 找到匹配应用程序: ukui-search (匹配得分: 22)
[2025-08-04 23:28:06]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search' (得分: 22 < 90)，不进行详细控件查找
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='sogouimeservice' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[44]: 'sogouImeService' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='ksc-defender' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[45]: 'ksc-defender' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='xdg-desktop-portal-gtk' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[46]: 'xdg-desktop-portal-gtk' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='mate-terminal' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[47]: 'mate-terminal' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='accerciser' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[48]: 'accerciser' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG]   智能匹配检查: 应用程序='文件管理器' vs 窗口类名='ukui-menu.ukui-menu'
[2025-08-04 23:28:06]  [DEBUG]   过滤通用标识符: 'ukui'
[2025-08-04 23:28:06]  [DEBUG]   提取的应用标识符: ['ukui-menu', 'menu.ukui', 'menu', 'ukui-menu.ukui-menu']
[2025-08-04 23:28:06]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:06]  [DEBUG] 应用程序[49]: '文件管理器' -> 匹配得分: 0
[2025-08-04 23:28:06]  [DEBUG] === 第一轮精确匹配完成 ===
[2025-08-04 23:28:06]  [DEBUG] 所有应用程序: [0] 'panel-daemon', [1] 'sni-daemon', [2] 'ukui-session', [3] 'sni-xembed-proxy', [4] 'ukuismserver'...
[2025-08-04 23:28:06]  [DEBUG] ✅ 找到高精度匹配控件: 得分=103
[2025-08-04 23:28:06]  [DEBUG] X11层级检测成功找到控件: unnamed
[2025-08-04 23:28:06]  [INFO] 使用X11层级检测在坐标 (189, 385) 处找到最顶层控件: unnamed
[2025-08-04 23:28:06]  [UNI] 查找控件结果: [list item | ]
[2025-08-04 23:28:06]  [UNI] 控件ParentPath: [0, 0, 0, 1, 0, 0, 3]
[2025-08-04 23:28:06]  [UNI] ParentPath第一个节点索引: 0
[2025-08-04 23:28:06]  [UNI] 找到应用程序对象: ukui-menu
[2025-08-04 23:28:06]  [UNI] ParentPath第一个节点对象:  (角色: frame)
[2025-08-04 23:28:06]  [UNI] 更新WindowRoleName为ParentPath第一个节点角色: frame
[2025-08-04 23:28:06]  [UNI] 提取到控件信息带窗口: {'Name': 'N/A', 'ID': -1, 'ProcessID': 3551, 'Rolename': 'list item', 'Description': 'N/A', 'Index_in_parent': 3, 'ChildrenCount': 0, 'ProcessName': 'ukui-menu', 'Coords': {'x': 15, 'y': 367, 'width': 298, 'height': 44}, 'Text': 'Not available: ', 'Actions': ['Toggle'], 'States': ['enabled', 'focusable', 'selectable', 'sensitive', 'showing', 'transient', 'visible'], 'ParentPath': [0, 0, 0, 1, 0, 0, 3], 'ParentCount': 7, 'Key': 'NNA-DNA-P0001003', 'RecordPosition': (189, 385), 'WindowRoleName': 'frame', 'WindowChildCount': 1}
[2025-08-04 23:28:06]  [UNI] 控件ParentPath: [0, 0, 0, 1, 0, 0, 3]
[2025-08-04 23:28:06]  [UNI] ParentPath第一个节点索引: 0
[2025-08-04 23:28:06]  [UNI] 找到应用程序对象: ukui-menu
[2025-08-04 23:28:06]  [UNI] ParentPath第一个节点对象:  (角色: frame)
[2025-08-04 23:28:06]  [UNI] 控件实际所属窗口: 
[2025-08-04 23:28:06]  [UNI] 提取到控件信息带窗口: {'Name': 'N/A', 'ID': -1, 'ProcessID': 3551, 'Rolename': 'list item', 'Description': 'N/A', 'Index_in_parent': 3, 'ChildrenCount': 0, 'ProcessName': 'ukui-menu', 'Coords': {'x': 15, 'y': 367, 'width': 298, 'height': 44}, 'Text': 'Not available: ', 'Actions': ['Toggle'], 'States': ['enabled', 'focusable', 'selectable', 'sensitive', 'showing', 'transient', 'visible'], 'ParentPath': [0, 0, 0, 1, 0, 0, 3], 'ParentCount': 7, 'Key': 'NNA-DNA-P0001003', 'RecordPosition': (189, 385), 'WindowRoleName': 'frame', 'WindowChildCount': 1, 'WindowName': ''}
[2025-08-04 23:28:06]  [UNI] 控件名称生成为: 列表项_(15,367)
[2025-08-04 23:28:06]  [UNI] 控件信息验证通过
[2025-08-04 23:28:06]  🐌 [INFO] 控件识别耗时: 0.203秒 | 方法: analyze_widget_at_with_new_app_detection | 坐标: (189, 385)
[2025-08-04 23:28:06]  [INFO] 控件识别完成，耗时: 0.203秒
[2025-08-04 23:28:06]  
============================================================
[2025-08-04 23:28:06]  [SUCCESS] 成功识别控件信息:
[2025-08-04 23:28:06]    名称: 列表项_(15,367)
[2025-08-04 23:28:06]    类型: Unknown
[2025-08-04 23:28:06]    进程: ukui-menu
[2025-08-04 23:28:06]    位置: x=15, y=367, 宽=298, 高=44
[2025-08-04 23:28:06]  ============================================================

[2025-08-04 23:28:06]  [INFO] 🎯 悬停识别成功并缓存: 列表项_(15,367) (进程: ukui-menu)
[2025-08-04 23:28:07]  [INFO] 记录拖动起始点: (189, 385) 按钮=left
[2025-08-04 23:28:07]  [INFO] 鼠标释放检查: 起始(189, 385) 结束(189, 385) 距离=0.0px 阈值=10px
[2025-08-04 23:28:07]  [INFO] 距离不足，不是拖动: 0.0px < 10px
[2025-08-04 23:28:07]  
************************************************************
[2025-08-04 23:28:07]  [CLICK] 点击控件信息:
[2025-08-04 23:28:07]    名称: 列表项_(15,367)
[2025-08-04 23:28:07]    位置: (189, 385)
[2025-08-04 23:28:07]  ************************************************************

[2025-08-04 23:28:07]  [EVENT] 记录click事件: 位置=(189, 385), 控件=列表项_(15,367)
[2025-08-04 23:28:08]  [INFO] 悬停超时触发: 坐标=(780, 264)
[2025-08-04 23:28:08]  [INFO] 开始控件识别...
[2025-08-04 23:28:08]  [UNI] 开始查找坐标(780, 264)处的控件
[2025-08-04 23:28:08]  监听文件无内容，非菜单控件： Expecting value: line 1 column 1 (char 0)
[2025-08-04 23:28:08]  [DEBUG] 获取新的桌面对象
[2025-08-04 23:28:08]  [DEBUG] 桌面对象获取完成，应用程序数量: 51
[2025-08-04 23:28:08]  [DEBUG] 已触发桌面刷新，应用数: 51
[2025-08-04 23:28:08]  [UNI] 获取到活动窗口: [frame | ], 进程ID: 2838
[2025-08-04 23:28:08]  [DEBUG] 使用缓存的桌面对象
[2025-08-04 23:28:08]  [DEBUG] 🚨 开始警告窗口专项检测，坐标: (780, 264)
[2025-08-04 23:28:08]  [DEBUG] 查找X11窗口对应的AT-SPI控件，窗口类名: Unknown
[2025-08-04 23:28:08]  [DEBUG] ⚡ 窗口类名无效 ('Unknown')，跳过AT-SPI控件识别以避免卡顿
[2025-08-04 23:28:08]  [DEBUG] ⚡ 检测到Unknown窗口，完全跳过控件识别以避免卡顿
[2025-08-04 23:28:08]  [UNI] 查找控件结果: None
[2025-08-04 23:28:08]  [UNI] 未找到位置为(780, 264)的控件
[2025-08-04 23:28:09]  [INFO] 记录拖动起始点: (776, 266) 按钮=left
[2025-08-04 23:28:09]  [UNI] 开始查找坐标(780, 264)处的控件
[2025-08-04 23:28:09]  监听文件无内容，非菜单控件： Expecting value: line 1 column 1 (char 0)
[2025-08-04 23:28:09]  [DEBUG] 获取新的桌面对象
[2025-08-04 23:28:09]  [DEBUG] 桌面对象获取完成，应用程序数量: 51
[2025-08-04 23:28:09]  [DEBUG] 已触发桌面刷新，应用数: 51
[2025-08-04 23:28:09]  [INFO] 鼠标释放检查: 起始(776, 266) 结束(776, 266) 距离=0.0px 阈值=10px
[2025-08-04 23:28:09]  [INFO] 距离不足，不是拖动: 0.0px < 10px
[2025-08-04 23:28:09]  [EVENT] 记录click事件: 位置=(776, 266), 控件=Unknown
[2025-08-04 23:28:09]  [UNI] 获取到活动窗口: [frame | ], 进程ID: 2838
[2025-08-04 23:28:09]  [DEBUG] 使用缓存的桌面对象
[2025-08-04 23:28:09]  [DEBUG] 🚨 开始警告窗口专项检测，坐标: (780, 264)
[2025-08-04 23:28:09]  [DEBUG] 查找X11窗口对应的AT-SPI控件，窗口类名: mate-terminal.Mate-terminal
[2025-08-04 23:28:09]  [DEBUG] 使用缓存的桌面对象
[2025-08-04 23:28:09]  [DEBUG] 当前桌面应用程序数量: 50
[2025-08-04 23:28:09]  [DEBUG] 目标窗口类名: 'mate-terminal.Mate-terminal'
[2025-08-04 23:28:09]  [DEBUG] 应用程序数量未变化，尝试强制刷新AT-SPI
[2025-08-04 23:28:09]  [DEBUG] 强制刷新后应用程序数量: 50
[2025-08-04 23:28:09]  [DEBUG] === 开始遍历所有AT-SPI应用程序 ===
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='panel-daemon' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[0]: 'panel-daemon' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='sni-daemon' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[1]: 'sni-daemon' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='ukui-session' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[2]: 'ukui-session' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='sni-xembed-proxy' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[3]: 'sni-xembed-proxy' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='ukuismserver' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[4]: 'ukuismserver' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='桌面' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[5]: '桌面' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='kglobalaccel' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[6]: 'kglobalaccel' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='ukui-panel' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[7]: 'ukui-panel' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='ukui-kwin' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[8]: 'ukui-kwin' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='ukui-watermark' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[9]: 'ukui-watermark' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='screenmonitorgeneral' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[10]: 'screenMonitorGeneral' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='用户手册' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[11]: '用户手册' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='sogou-qimpanel-watchdog' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ✅ 语义相似度匹配: 得分=1
[2025-08-04 23:28:09]  [DEBUG] 应用程序[12]: 'sogou-qimpanel-watchdog' -> 匹配得分: 1
[2025-08-04 23:28:09]  [DEBUG] ✅ 找到匹配应用程序: sogou-qimpanel-watchdog (匹配得分: 1)
[2025-08-04 23:28:09]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'sogou-qimpanel-watchdog' (得分: 1 < 90)，不进行详细控件查找
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='ukui-upower' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[13]: 'ukui-upower' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='vdclient' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ✅ 语义相似度匹配: 得分=3
[2025-08-04 23:28:09]  [DEBUG] 应用程序[14]: 'vdclient' -> 匹配得分: 3
[2025-08-04 23:28:09]  [DEBUG] ✅ 找到匹配应用程序: vdclient (匹配得分: 3)
[2025-08-04 23:28:09]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'vdclient' (得分: 3 < 90)，不进行详细控件查找
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='kscreen_backend_launcher' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ✅ 语义相似度匹配: 得分=1
[2025-08-04 23:28:09]  [DEBUG] 应用程序[15]: 'kscreen_backend_launcher' -> 匹配得分: 1
[2025-08-04 23:28:09]  [DEBUG] ✅ 找到匹配应用程序: kscreen_backend_launcher (匹配得分: 1)
[2025-08-04 23:28:09]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'kscreen_backend_launcher' (得分: 1 < 90)，不进行详细控件查找
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='secriskbox' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[16]: 'secRiskBox' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='ukui-power-manager-tray' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[17]: 'ukui-power-manager-tray' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='ukui-sidebar' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[18]: 'ukui-sidebar' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='ukui-notifications' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[19]: 'ukui-notifications' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[20]: 'NotifySend' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[21]: 'ukui-settings-daemon' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[22]: 'ukui-settings-daemon' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[23]: 'NotifySend' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='ukui-powermanagement' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[24]: 'ukui-powermanagement' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='ukui-volume-control-applet-qt' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[25]: 'ukui-volume-control-applet-qt' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='vino-server' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[26]: 'vino-server' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='麒麟id' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[27]: '麒麟ID' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='kylin-device-daemon' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[28]: 'kylin-device-daemon' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='kylin-process-manager' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ✅ 语义相似度匹配: 得分=1
[2025-08-04 23:28:09]  [DEBUG] 应用程序[29]: 'kylin-process-manager' -> 匹配得分: 1
[2025-08-04 23:28:09]  [DEBUG] ✅ 找到匹配应用程序: kylin-process-manager (匹配得分: 1)
[2025-08-04 23:28:09]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'kylin-process-manager' (得分: 1 < 90)，不进行详细控件查找
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='ukui-bluetooth' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[30]: 'ukui-bluetooth' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='polkit-ukui-authentication-agent-1' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[31]: 'polkit-ukui-authentication-agent-1' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='天气' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[32]: '天气' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='kylin-vpn' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[33]: 'kylin-vpn' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='kylin note' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[34]: 'Kylin Note' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[35]: 'kylin-nm' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[36]: 'kylin-nm' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='kylin-printer-applet' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ✅ 语义相似度匹配: 得分=1
[2025-08-04 23:28:09]  [DEBUG] 应用程序[37]: 'kylin-printer-applet' -> 匹配得分: 1
[2025-08-04 23:28:09]  [DEBUG] ✅ 找到匹配应用程序: kylin-printer-applet (匹配得分: 1)
[2025-08-04 23:28:09]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'kylin-printer-applet' (得分: 1 < 90)，不进行详细控件查找
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='ukui-menu' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[38]: 'ukui-menu' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='ukui-search-service-dir-manager' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[39]: 'ukui-search-service-dir-manager' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='ukui-search-app-data-service' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[40]: 'ukui-search-app-data-service' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='ukui-search-service' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[41]: 'ukui-search-service' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='prlcc' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[42]: 'prlcc' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='ukui-search' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[43]: 'ukui-search' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='sogouimeservice' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[44]: 'sogouImeService' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='ksc-defender' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[45]: 'ksc-defender' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='xdg-desktop-portal-gtk' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[46]: 'xdg-desktop-portal-gtk' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='mate-terminal' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ✅ 反向标识符匹配(mate-terminal): 得分=111
[2025-08-04 23:28:09]  [DEBUG] 应用程序[47]: 'mate-terminal' -> 匹配得分: 111
[2025-08-04 23:28:09]  [DEBUG] ✅ 找到匹配应用程序: mate-terminal (匹配得分: 111)
[2025-08-04 23:28:09]  [DEBUG] 🔍 高匹配度应用程序 (得分: 111)，开始详细控件查找...
[2025-08-04 23:28:09]  [DEBUG] 🔍 开始在应用程序 'mate-terminal' 中查找坐标 (780, 264) 处的控件...
[2025-08-04 23:28:09]  [DEBUG]     找到包含坐标的元素: kylin@kylin-pc: ~/kylin-robot-ide (角色: frame, 深度: 0, 子控件数: 1)
[2025-08-04 23:28:09]  [DEBUG]     检查子控件[0] 'unnamed' (filler): 坐标(701, 290) 大小652x440
[2025-08-04 23:28:09]  [DEBUG]     ❌ 子控件[0] 'unnamed' 不包含目标坐标(780, 264)
[2025-08-04 23:28:09]  [DEBUG] ❌ 在高匹配度应用程序 'mate-terminal' 中未找到包含坐标 (780, 264) 的控件
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='accerciser' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[48]: 'accerciser' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG]   智能匹配检查: 应用程序='文件管理器' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:09]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:09]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:09]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:09]  [DEBUG] 应用程序[49]: '文件管理器' -> 匹配得分: 0
[2025-08-04 23:28:09]  [DEBUG] === 第一轮精确匹配完成 ===
[2025-08-04 23:28:09]  [DEBUG] 所有应用程序: [0] 'panel-daemon', [1] 'sni-daemon', [2] 'ukui-session', [3] 'sni-xembed-proxy', [4] 'ukuismserver'...
[2025-08-04 23:28:09]  [DEBUG] ❌ 未找到高精度匹配的控件 (阈值: 90分)，窗口类名: mate-terminal.Mate-terminal
[2025-08-04 23:28:09]  [DEBUG] X11层级检测未找到匹配的AT-SPI控件
[2025-08-04 23:28:09]  [DEBUG] ⚡ X11层级检测失败，使用智能深度搜索方法，坐标: (780, 264)
[2025-08-04 23:28:09]  [DEBUG] 🧠 开始智能深度搜索，坐标: (780, 264), 最大深度: 25
[2025-08-04 23:28:09]  [DEBUG]   找到候选控件: unnamed (frame) 面积:1253448 深度:0
[2025-08-04 23:28:09]  [DEBUG]     找到候选控件: unnamed (filler) 面积:1253448 深度:1
[2025-08-04 23:28:09]  [DEBUG]       找到候选控件: unnamed (filler) 面积:1183896 深度:2
[2025-08-04 23:28:09]  [DEBUG]     深度得分: +0 (深度:0)
[2025-08-04 23:28:09]  [DEBUG]     面积得分: +0.0 (面积:1253448)
[2025-08-04 23:28:09]  [DEBUG]     角色得分: +0 (角色:frame)
[2025-08-04 23:28:09]  [DEBUG]     位置得分: +2.4 (距离中心:151.9)
[2025-08-04 23:28:09]  [DEBUG]     总得分: 2.4
[2025-08-04 23:28:09]  [DEBUG]     深度得分: +15 (深度:1)
[2025-08-04 23:28:09]  [DEBUG]     面积得分: +0.0 (面积:1253448)
[2025-08-04 23:28:09]  [DEBUG]     角色得分: +10 (角色:filler)
[2025-08-04 23:28:09]  [DEBUG]     位置得分: +2.4 (距离中心:151.9)
[2025-08-04 23:28:09]  [DEBUG]     总得分: 27.4
[2025-08-04 23:28:09]  [DEBUG]     深度得分: +30 (深度:2)
[2025-08-04 23:28:09]  [DEBUG]     面积得分: +0.0 (面积:1183896)
[2025-08-04 23:28:09]  [DEBUG]     角色得分: +10 (角色:filler)
[2025-08-04 23:28:09]  [DEBUG]     位置得分: +3.5 (距离中心:129.2)
[2025-08-04 23:28:09]  [DEBUG]     总得分: 43.5
[2025-08-04 23:28:09]  [DEBUG] 🎯 发现 2 个filler控件，优先选择面积最小的
[2025-08-04 23:28:09]  [DEBUG] 🎯 选择最小filler: unnamed 面积:1183896
[2025-08-04 23:28:09]  [DEBUG] ✅ 智能深度搜索完成，找到最佳控件: unnamed (filler) 得分:43.5
[2025-08-04 23:28:09]  [DEBUG]   排名1: unnamed (frame) 得分:2.4 面积:1253448
[2025-08-04 23:28:09]  [DEBUG]   排名2: unnamed (filler) 得分:27.4 面积:1253448
[2025-08-04 23:28:09]  [DEBUG]   排名3: unnamed (filler) 得分:43.5 面积:1183896
[2025-08-04 23:28:09]  [INFO] 智能深度搜索在坐标 (780, 264) 处找到最佳控件: unnamed
[2025-08-04 23:28:09]  [UNI] 查找控件结果: [filler | ]
[2025-08-04 23:28:09]  [UNI] 控件ParentPath: [1, 0, 0]
[2025-08-04 23:28:09]  [UNI] ParentPath第一个节点索引: 1
[2025-08-04 23:28:09]  [UNI] 找到应用程序对象: 桌面
[2025-08-04 23:28:09]  [UNI] ParentPath第一个节点对象:  (角色: frame)
[2025-08-04 23:28:09]  [UNI] 更新WindowRoleName为ParentPath第一个节点角色: frame
[2025-08-04 23:28:09]  [UNI] 提取到控件信息带窗口: {'Name': 'N/A', 'ID': -1, 'ProcessID': 2838, 'Rolename': 'filler', 'Description': 'N/A', 'Index_in_parent': 'N/A', 'ChildrenCount': 0, 'ProcessName': 'peony-qt-deskto', 'Coords': {'x': 0, 'y': 0, 'width': 1512, 'height': 783}, 'Text': 'Not available: ', 'Actions': [], 'States': ['enabled', 'sensitive', 'showing', 'visible'], 'ParentPath': [1, 0, 0], 'ParentCount': 3, 'Key': 'NNA-DNA-P100', 'RecordPosition': (780, 264), 'WindowRoleName': 'frame', 'WindowChildCount': 1}
[2025-08-04 23:28:09]  [UNI] 控件ParentPath: [1, 0, 0]
[2025-08-04 23:28:09]  [UNI] ParentPath第一个节点索引: 1
[2025-08-04 23:28:09]  [UNI] 找到应用程序对象: 桌面
[2025-08-04 23:28:09]  [UNI] ParentPath第一个节点对象:  (角色: frame)
[2025-08-04 23:28:09]  [UNI] 控件实际所属窗口: 
[2025-08-04 23:28:09]  [UNI] 提取到控件信息带窗口: {'Name': 'N/A', 'ID': -1, 'ProcessID': 2838, 'Rolename': 'filler', 'Description': 'N/A', 'Index_in_parent': 'N/A', 'ChildrenCount': 0, 'ProcessName': 'peony-qt-deskto', 'Coords': {'x': 0, 'y': 0, 'width': 1512, 'height': 783}, 'Text': 'Not available: ', 'Actions': [], 'States': ['enabled', 'sensitive', 'showing', 'visible'], 'ParentPath': [1, 0, 0], 'ParentCount': 3, 'Key': 'NNA-DNA-P100', 'RecordPosition': (780, 264), 'WindowRoleName': 'frame', 'WindowChildCount': 1, 'WindowName': ''}
[2025-08-04 23:28:09]  [UNI] 控件名称生成为: 填充区域_(0,0)
[2025-08-04 23:28:09]  [UNI] 控件信息验证通过
[2025-08-04 23:28:09]  [INFO] 控件识别被中断（悬停超时回调）
[2025-08-04 23:28:11]  [INFO] 悬停超时触发: 坐标=(503, 621)
[2025-08-04 23:28:11]  [INFO] _perform_deep_atspi_recovery,耗时：0.012422561645507812
[2025-08-04 23:28:11]  ⚡ [FAST] 控件识别快速: 0.012秒 | 方法: deep_atspi_recovery | 坐标: (, )
[2025-08-04 23:28:11]  [INFO] 开始控件识别...
[2025-08-04 23:28:11]  [UNI] 开始查找坐标(503, 621)处的控件
[2025-08-04 23:28:11]  监听文件无内容，非菜单控件： Expecting value: line 1 column 1 (char 0)
[2025-08-04 23:28:11]  [DEBUG] 获取新的桌面对象
[2025-08-04 23:28:11]  [DEBUG] 桌面对象获取完成，应用程序数量: 50
[2025-08-04 23:28:11]  [DEBUG] 已触发桌面刷新，应用数: 50
[2025-08-04 23:28:11]  [UNI] 获取到活动窗口: [frame | kylin@kylin-pc: ~/kylin-robot-ide/scripts/recorder], 进程ID: 129851
[2025-08-04 23:28:11]  [DEBUG] 使用缓存的桌面对象
[2025-08-04 23:28:11]  [DEBUG] 🚨 开始警告窗口专项检测，坐标: (503, 621)
[2025-08-04 23:28:11]  [DEBUG] 查找X11窗口对应的AT-SPI控件，窗口类名: mate-terminal.Mate-terminal
[2025-08-04 23:28:11]  [DEBUG] 使用缓存的桌面对象
[2025-08-04 23:28:11]  [DEBUG] 当前桌面应用程序数量: 50
[2025-08-04 23:28:11]  [DEBUG] 目标窗口类名: 'mate-terminal.Mate-terminal'
[2025-08-04 23:28:11]  [DEBUG] 应用程序数量未变化，尝试强制刷新AT-SPI
[2025-08-04 23:28:11]  [DEBUG] 强制刷新后应用程序数量: 50
[2025-08-04 23:28:11]  [DEBUG] === 开始遍历所有AT-SPI应用程序 ===
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='panel-daemon' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[0]: 'panel-daemon' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='sni-daemon' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[1]: 'sni-daemon' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='ukui-session' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[2]: 'ukui-session' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='sni-xembed-proxy' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[3]: 'sni-xembed-proxy' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='ukuismserver' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[4]: 'ukuismserver' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='桌面' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[5]: '桌面' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='kglobalaccel' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[6]: 'kglobalaccel' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='ukui-panel' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[7]: 'ukui-panel' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='ukui-kwin' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[8]: 'ukui-kwin' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='ukui-watermark' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[9]: 'ukui-watermark' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='screenmonitorgeneral' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[10]: 'screenMonitorGeneral' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='用户手册' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[11]: '用户手册' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='sogou-qimpanel-watchdog' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ✅ 语义相似度匹配: 得分=1
[2025-08-04 23:28:11]  [DEBUG] 应用程序[12]: 'sogou-qimpanel-watchdog' -> 匹配得分: 1
[2025-08-04 23:28:11]  [DEBUG] ✅ 找到匹配应用程序: sogou-qimpanel-watchdog (匹配得分: 1)
[2025-08-04 23:28:11]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'sogou-qimpanel-watchdog' (得分: 1 < 90)，不进行详细控件查找
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='ukui-upower' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[13]: 'ukui-upower' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='vdclient' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ✅ 语义相似度匹配: 得分=3
[2025-08-04 23:28:11]  [DEBUG] 应用程序[14]: 'vdclient' -> 匹配得分: 3
[2025-08-04 23:28:11]  [DEBUG] ✅ 找到匹配应用程序: vdclient (匹配得分: 3)
[2025-08-04 23:28:11]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'vdclient' (得分: 3 < 90)，不进行详细控件查找
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='kscreen_backend_launcher' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ✅ 语义相似度匹配: 得分=1
[2025-08-04 23:28:11]  [DEBUG] 应用程序[15]: 'kscreen_backend_launcher' -> 匹配得分: 1
[2025-08-04 23:28:11]  [DEBUG] ✅ 找到匹配应用程序: kscreen_backend_launcher (匹配得分: 1)
[2025-08-04 23:28:11]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'kscreen_backend_launcher' (得分: 1 < 90)，不进行详细控件查找
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='secriskbox' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[16]: 'secRiskBox' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='ukui-power-manager-tray' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[17]: 'ukui-power-manager-tray' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='ukui-sidebar' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[18]: 'ukui-sidebar' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='ukui-notifications' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[19]: 'ukui-notifications' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[20]: 'NotifySend' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[21]: 'ukui-settings-daemon' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[22]: 'ukui-settings-daemon' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[23]: 'NotifySend' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='ukui-powermanagement' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[24]: 'ukui-powermanagement' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='ukui-volume-control-applet-qt' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[25]: 'ukui-volume-control-applet-qt' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='vino-server' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[26]: 'vino-server' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='麒麟id' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[27]: '麒麟ID' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='kylin-device-daemon' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[28]: 'kylin-device-daemon' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='kylin-process-manager' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ✅ 语义相似度匹配: 得分=1
[2025-08-04 23:28:11]  [DEBUG] 应用程序[29]: 'kylin-process-manager' -> 匹配得分: 1
[2025-08-04 23:28:11]  [DEBUG] ✅ 找到匹配应用程序: kylin-process-manager (匹配得分: 1)
[2025-08-04 23:28:11]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'kylin-process-manager' (得分: 1 < 90)，不进行详细控件查找
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='ukui-bluetooth' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[30]: 'ukui-bluetooth' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='polkit-ukui-authentication-agent-1' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[31]: 'polkit-ukui-authentication-agent-1' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='天气' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[32]: '天气' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='kylin-vpn' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[33]: 'kylin-vpn' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='kylin note' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[34]: 'Kylin Note' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[35]: 'kylin-nm' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[36]: 'kylin-nm' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='kylin-printer-applet' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ✅ 语义相似度匹配: 得分=1
[2025-08-04 23:28:11]  [DEBUG] 应用程序[37]: 'kylin-printer-applet' -> 匹配得分: 1
[2025-08-04 23:28:11]  [DEBUG] ✅ 找到匹配应用程序: kylin-printer-applet (匹配得分: 1)
[2025-08-04 23:28:11]  [DEBUG] ⏭️ 跳过低匹配度应用程序 'kylin-printer-applet' (得分: 1 < 90)，不进行详细控件查找
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='ukui-menu' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[38]: 'ukui-menu' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='ukui-search-service-dir-manager' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[39]: 'ukui-search-service-dir-manager' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='ukui-search-app-data-service' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[40]: 'ukui-search-app-data-service' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='ukui-search-service' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[41]: 'ukui-search-service' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='prlcc' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[42]: 'prlcc' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='ukui-search' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[43]: 'ukui-search' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='sogouimeservice' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[44]: 'sogouImeService' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='ksc-defender' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[45]: 'ksc-defender' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='xdg-desktop-portal-gtk' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[46]: 'xdg-desktop-portal-gtk' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='mate-terminal' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ✅ 反向标识符匹配(mate-terminal): 得分=111
[2025-08-04 23:28:11]  [DEBUG] 应用程序[47]: 'mate-terminal' -> 匹配得分: 111
[2025-08-04 23:28:11]  [DEBUG] ✅ 找到匹配应用程序: mate-terminal (匹配得分: 111)
[2025-08-04 23:28:11]  [DEBUG] 🔍 高匹配度应用程序 (得分: 111)，开始详细控件查找...
[2025-08-04 23:28:11]  [DEBUG] 🔍 开始在应用程序 'mate-terminal' 中查找坐标 (503, 621) 处的控件...
[2025-08-04 23:28:11]  [DEBUG]     找到包含坐标的元素: kylin@kylin-pc: ~/kylin-robot-ide/scripts/recorder (角色: frame, 深度: 0, 子控件数: 1)
[2025-08-04 23:28:11]  [DEBUG]     检查子控件[0] 'unnamed' (filler): 坐标(83, 86) 大小684x678
[2025-08-04 23:28:11]  [DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(503, 621)
[2025-08-04 23:28:11]  [DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (filler)
[2025-08-04 23:28:11]  [DEBUG]     找到包含坐标的元素: unnamed (角色: filler, 深度: 1, 子控件数: 2)
[2025-08-04 23:28:11]  [DEBUG]     检查子控件[0] 'unnamed' (menu bar): 坐标(83, 86) 大小684x30
[2025-08-04 23:28:11]  [DEBUG]     ❌ 子控件[0] 'unnamed' 不包含目标坐标(503, 621)
[2025-08-04 23:28:11]  [DEBUG]     检查子控件[1] 'unnamed' (page tab list): 坐标(83, 116) 大小684x648
[2025-08-04 23:28:11]  [DEBUG]     ✅ 子控件[1] 'unnamed' 包含目标坐标(503, 621)
[2025-08-04 23:28:11]  [DEBUG]     🔍 强制搜索 page tab list 子控件[1] 'unnamed'
[2025-08-04 23:28:11]  [DEBUG]     找到包含坐标的元素: unnamed (角色: page tab list, 深度: 2, 子控件数: 1)
[2025-08-04 23:28:11]  [DEBUG]     检查子控件[0] 'unnamed' (page tab): 坐标(83, 116) 大小0x0
[2025-08-04 23:28:11]  [DEBUG]     ❌ 子控件[0] 'unnamed' 不包含目标坐标(503, 621)
[2025-08-04 23:28:11]  [DEBUG]     🚫 page tab list 超低优先级: -200
[2025-08-04 23:28:11]  [DEBUG]     找到 1 个候选子控件:
[2025-08-04 23:28:11]  [DEBUG]       [0] unnamed (角色: page tab list, 得分: -170, 面积: 443232, 分支: 1)
[2025-08-04 23:28:11]  [DEBUG]     🔍 只找到容器控件，继续深入搜索...
[2025-08-04 23:28:11]  [DEBUG]     🔍 深入搜索容器: unnamed (角色: page tab list)
[2025-08-04 23:28:11]  [DEBUG]       深度2: 开始搜索容器 'unnamed' (page tab list)，子控件数: 1
[2025-08-04 23:28:11]  [DEBUG]       深度2: 检查子控件[0] 'unnamed' (page tab)
[2025-08-04 23:28:11]  [DEBUG]       深度2: 子控件坐标: (83, 116), 大小: 0x0
[2025-08-04 23:28:11]  [DEBUG]       深度2: ❌ 子控件不包含目标坐标: unnamed (角色: page tab)
[2025-08-04 23:28:11]  [DEBUG]       深度2: 容器搜索完成，未找到交互控件
[2025-08-04 23:28:11]  [DEBUG]     ⚠️ 选择得分最高控件: unnamed (角色: page tab list)
[2025-08-04 23:28:11]  [DEBUG]     🚫 page tab list 超低优先级: -200
[2025-08-04 23:28:11]  [DEBUG]     找到 1 个候选子控件:
[2025-08-04 23:28:11]  [DEBUG]       [0] unnamed (角色: page tab list, 得分: -180, 面积: 443232, 分支: 0)
[2025-08-04 23:28:11]  [DEBUG]     🔍 只找到容器控件，继续深入搜索...
[2025-08-04 23:28:11]  [DEBUG]     🔍 深入搜索容器: unnamed (角色: page tab list)
[2025-08-04 23:28:11]  [DEBUG]       深度1: 开始搜索容器 'unnamed' (page tab list)，子控件数: 1
[2025-08-04 23:28:11]  [DEBUG]       深度1: 检查子控件[0] 'unnamed' (page tab)
[2025-08-04 23:28:11]  [DEBUG]       深度1: 子控件坐标: (83, 116), 大小: 0x0
[2025-08-04 23:28:11]  [DEBUG]       深度1: ❌ 子控件不包含目标坐标: unnamed (角色: page tab)
[2025-08-04 23:28:11]  [DEBUG]       深度1: 容器搜索完成，未找到交互控件
[2025-08-04 23:28:11]  [DEBUG]     ⚠️ 选择得分最高控件: unnamed (角色: page tab list)
[2025-08-04 23:28:11]  [DEBUG]     找到包含坐标的元素: kylin@kylin-pc: /opt/KylinRobot-v2/testcase/GUI/OCR (角色: frame, 深度: 0, 子控件数: 1)
[2025-08-04 23:28:11]  [DEBUG]     检查子控件[0] 'unnamed' (filler): 坐标(144, 326) 大小1076x440
[2025-08-04 23:28:11]  [DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(503, 621)
[2025-08-04 23:28:11]  [DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (filler)
[2025-08-04 23:28:11]  [DEBUG]     找到包含坐标的元素: unnamed (角色: filler, 深度: 1, 子控件数: 2)
[2025-08-04 23:28:11]  [DEBUG]     检查子控件[0] 'unnamed' (menu bar): 坐标(144, 326) 大小1076x30
[2025-08-04 23:28:11]  [DEBUG]     ❌ 子控件[0] 'unnamed' 不包含目标坐标(503, 621)
[2025-08-04 23:28:11]  [DEBUG]     检查子控件[1] 'unnamed' (page tab list): 坐标(144, 356) 大小1076x410
[2025-08-04 23:28:11]  [DEBUG]     ✅ 子控件[1] 'unnamed' 包含目标坐标(503, 621)
[2025-08-04 23:28:11]  [DEBUG]     🔍 强制搜索 page tab list 子控件[1] 'unnamed'
[2025-08-04 23:28:11]  [DEBUG]     找到包含坐标的元素: unnamed (角色: page tab list, 深度: 2, 子控件数: 1)
[2025-08-04 23:28:11]  [DEBUG]     检查子控件[0] 'unnamed' (page tab): 坐标(144, 356) 大小0x0
[2025-08-04 23:28:11]  [DEBUG]     ❌ 子控件[0] 'unnamed' 不包含目标坐标(503, 621)
[2025-08-04 23:28:11]  [DEBUG]     🚫 page tab list 超低优先级: -200
[2025-08-04 23:28:11]  [DEBUG]     找到 1 个候选子控件:
[2025-08-04 23:28:11]  [DEBUG]       [0] unnamed (角色: page tab list, 得分: -170, 面积: 441160, 分支: 1)
[2025-08-04 23:28:11]  [DEBUG]     🔍 只找到容器控件，继续深入搜索...
[2025-08-04 23:28:11]  [DEBUG]     🔍 深入搜索容器: unnamed (角色: page tab list)
[2025-08-04 23:28:11]  [DEBUG]       深度2: 开始搜索容器 'unnamed' (page tab list)，子控件数: 1
[2025-08-04 23:28:11]  [DEBUG]       深度2: 检查子控件[0] 'unnamed' (page tab)
[2025-08-04 23:28:11]  [DEBUG]       深度2: 子控件坐标: (144, 356), 大小: 0x0
[2025-08-04 23:28:11]  [DEBUG]       深度2: ❌ 子控件不包含目标坐标: unnamed (角色: page tab)
[2025-08-04 23:28:11]  [DEBUG]       深度2: 容器搜索完成，未找到交互控件
[2025-08-04 23:28:11]  [DEBUG]     ⚠️ 选择得分最高控件: unnamed (角色: page tab list)
[2025-08-04 23:28:11]  [DEBUG]     🚫 page tab list 超低优先级: -200
[2025-08-04 23:28:11]  [DEBUG]     找到 1 个候选子控件:
[2025-08-04 23:28:11]  [DEBUG]       [0] unnamed (角色: page tab list, 得分: -180, 面积: 441160, 分支: 0)
[2025-08-04 23:28:11]  [DEBUG]     🔍 只找到容器控件，继续深入搜索...
[2025-08-04 23:28:11]  [DEBUG]     🔍 深入搜索容器: unnamed (角色: page tab list)
[2025-08-04 23:28:11]  [DEBUG]       深度1: 开始搜索容器 'unnamed' (page tab list)，子控件数: 1
[2025-08-04 23:28:11]  [DEBUG]       深度1: 检查子控件[0] 'unnamed' (page tab)
[2025-08-04 23:28:11]  [DEBUG]       深度1: 子控件坐标: (144, 356), 大小: 0x0
[2025-08-04 23:28:11]  [DEBUG]       深度1: ❌ 子控件不包含目标坐标: unnamed (角色: page tab)
[2025-08-04 23:28:11]  [DEBUG]       深度1: 容器搜索完成，未找到交互控件
[2025-08-04 23:28:11]  [DEBUG]     ⚠️ 选择得分最高控件: unnamed (角色: page tab list)
[2025-08-04 23:28:11]  [DEBUG] 🔍 从 2 个候选控件中选择最佳控件
[2025-08-04 23:28:11]  [DEBUG]   候选控件: unnamed (角色: page tab list)
[2025-08-04 23:28:11]  [DEBUG]   候选控件: unnamed (角色: page tab list)
[2025-08-04 23:28:11]  [DEBUG] ⚠️ 选择容器控件: unnamed (角色: page tab list)
[2025-08-04 23:28:11]  [DEBUG] 🔄 检测到 page tab list，强制深入搜索所有子控件...
[2025-08-04 23:28:11]  [DEBUG] 🔍 开始强制深入搜索 page tab list，子控件数: 1
[2025-08-04 23:28:11]  [DEBUG] ❌ 强制深入搜索未找到合适的控件
[2025-08-04 23:28:11]  [DEBUG] ❌ 强制深入搜索未找到更好的控件
[2025-08-04 23:28:11]  [DEBUG] ✅ 在应用程序 'mate-terminal' 中找到控件: unnamed
[2025-08-04 23:28:11]  [DEBUG] 🎯 更新最佳匹配控件: unnamed (得分: 111)
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='accerciser' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[48]: 'accerciser' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG]   智能匹配检查: 应用程序='文件管理器' vs 窗口类名='mate-terminal.mate-terminal'
[2025-08-04 23:28:11]  [DEBUG]   过滤通用标识符: 'mate'
[2025-08-04 23:28:11]  [DEBUG]   提取的应用标识符: ['mate-terminal', 'terminal.mate', 'terminal', 'mate-terminal.mate-terminal']
[2025-08-04 23:28:11]  [DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[2025-08-04 23:28:11]  [DEBUG] 应用程序[49]: '文件管理器' -> 匹配得分: 0
[2025-08-04 23:28:11]  [DEBUG] === 第一轮精确匹配完成 ===
[2025-08-04 23:28:11]  [DEBUG] 所有应用程序: [0] 'panel-daemon', [1] 'sni-daemon', [2] 'ukui-session', [3] 'sni-xembed-proxy', [4] 'ukuismserver'...
[2025-08-04 23:28:11]  [DEBUG] ✅ 找到高精度匹配控件: 得分=111
[2025-08-04 23:28:11]  [DEBUG] X11层级检测成功找到控件: unnamed
[2025-08-04 23:28:11]  [DEBUG] 🔄 X11层级检测到 page tab list，强制深入搜索...
[2025-08-04 23:28:11]  [DEBUG] 🔍 开始强制深入搜索 page tab list，子控件数: 1
[2025-08-04 23:28:11]  [DEBUG] ❌ 强制深入搜索未找到合适的控件
[2025-08-04 23:28:11]  [DEBUG] ❌ X11层级深入搜索未找到更好的控件
[2025-08-04 23:28:11]  [INFO] 使用X11层级检测在坐标 (503, 621) 处找到最顶层控件: unnamed
[2025-08-04 23:28:11]  [UNI] 查找控件结果: [page tab list | ]
[2025-08-04 23:28:11]  [UNI] 控件ParentPath: [2, 0, 1]
[2025-08-04 23:28:11]  [UNI] ParentPath第一个节点索引: 2
[2025-08-04 23:28:11]  [UNI] 找到应用程序对象: mate-terminal
[2025-08-04 23:28:11]  [UNI] ParentPath第一个节点对象: kylin@kylin-pc: ~/kylin-robot-ide/scripts/recorder (角色: frame)
[2025-08-04 23:28:11]  [UNI] 更新WindowRoleName为ParentPath第一个节点角色: frame
[2025-08-04 23:28:11]  [UNI] 提取到控件信息带窗口: {'Name': 'N/A', 'ID': 123, 'ProcessID': 129851, 'Rolename': 'page tab list', 'Description': 'N/A', 'Index_in_parent': 1, 'ChildrenCount': 1, 'ProcessName': 'mate-terminal', 'Coords': {'x': 83, 'y': 116, 'width': 684, 'height': 648}, 'Text': 'Not available: ', 'Actions': 'Not available: ', 'States': ['enabled', 'sensitive', 'showing', 'visible'], 'ParentPath': [2, 0, 1], 'ParentCount': 3, 'Key': 'NNA-DNA-P201', 'RecordPosition': (503, 621), 'WindowRoleName': 'frame', 'WindowChildCount': 1}
[2025-08-04 23:28:11]  [UNI] 控件ParentPath: [2, 0, 1]
[2025-08-04 23:28:11]  [UNI] ParentPath第一个节点索引: 2
[2025-08-04 23:28:11]  [UNI] 找到应用程序对象: mate-terminal
[2025-08-04 23:28:11]  [UNI] ParentPath第一个节点对象: kylin@kylin-pc: ~/kylin-robot-ide/scripts/recorder (角色: frame)
[2025-08-04 23:28:11]  [UNI] 控件实际所属窗口: kylin@kylin-pc: ~/kylin-robot-ide/scripts/recorder
[2025-08-04 23:28:11]  [UNI] 提取到控件信息带窗口: {'Name': 'N/A', 'ID': 123, 'ProcessID': 129851, 'Rolename': 'page tab list', 'Description': 'N/A', 'Index_in_parent': 1, 'ChildrenCount': 1, 'ProcessName': 'mate-terminal', 'Coords': {'x': 83, 'y': 116, 'width': 684, 'height': 648}, 'Text': 'Not available: ', 'Actions': 'Not available: ', 'States': ['enabled', 'sensitive', 'showing', 'visible'], 'ParentPath': [2, 0, 1], 'ParentCount': 3, 'Key': 'NNA-DNA-P201', 'RecordPosition': (503, 621), 'WindowRoleName': 'frame', 'WindowChildCount': 1, 'WindowName': 'username@hostname: ~/username-robot-ide/scripts/recorder'}
[2025-08-04 23:28:11]  [UNI] 控件名称生成为: 标签页列表_(83,116)
[2025-08-04 23:28:11]  [UNI] 控件信息验证通过
[2025-08-04 23:28:11]  ⚡ [FAST] 控件识别快速: 0.138秒 | 方法: analyze_widget_at_with_new_app_detection | 坐标: (503, 621)
[2025-08-04 23:28:11]  [INFO] 控件识别完成，耗时: 0.138秒
[2025-08-04 23:28:11]  
============================================================
[2025-08-04 23:28:11]  [SUCCESS] 成功识别控件信息:
[2025-08-04 23:28:11]    名称: 标签页列表_(83,116)
[2025-08-04 23:28:11]    类型: Unknown
[2025-08-04 23:28:11]    进程: mate-terminal
[2025-08-04 23:28:11]    位置: x=83, y=116, 宽=684, 高=648
[2025-08-04 23:28:11]  ============================================================

[2025-08-04 23:28:11]  [INFO] 🎯 悬停识别成功并缓存: 标签页列表_(83,116) (进程: mate-terminal)
[2025-08-04 23:28:11]  [EVENT] 记录键盘press事件: 键=ctrl
[2025-08-04 23:28:11]  [EVENT] 记录键盘press事件: 键=c
[2025-08-04 23:28:11]  [INFO] 正在强制终止所有listenHF.py进程...
[2025-08-04 23:28:11]  [INFO] 没有找到运行中的listenHF.py进程
[2025-08-04 23:28:11]  [INFO] 悬停检测器已停止
[2025-08-04 23:28:11]  [INFO] 高亮渲染器已清理
[INFO] 开始执行全自动录制管理器...
参数信息为：Namespace(app_name=None, debug=False, duration=300, enable_command_listener=True, json_output=False, storage_path='recordings', test_case_id=None, testcase_path=None)
[2025-08-04 23:28:01]  [INFO] 开始录制会话: 5d9a052e-331f-4681-9c53-5af71315116e
[2025-08-04 23:28:01]  [INFO] 录制将在 300 秒后自动停止
[2025-08-04 23:28:01]  [INFO] 事件处理器已启动
[2025-08-04 23:28:01]  [INFO] 录制已启动，按Ctrl+C停止
[2025-08-04 23:28:01]  [INFO] 将鼠标悬停在控件上0.5秒可看到高亮，悬停3秒将录制控件信息
[2025-08-04 23:28:01]  [INFO] 可通过stdin发送命令: pause/resume/stop/status
[2025-08-04 23:28:04]  监听文件无内容，非菜单控件： Expecting value: line 1 column 1 (char 0)
[2025-08-04 23:28:04]  [INFO] 将点击事件加入到事件队列
[2025-08-04 23:28:06]  监听文件无内容，非菜单控件： Expecting value: line 1 column 1 (char 0)
[2025-08-04 23:28:07]  [INFO] 将点击事件加入到事件队列
[2025-08-04 23:28:08]  监听文件无内容，非菜单控件： Expecting value: line 1 column 1 (char 0)
[2025-08-04 23:28:09]  监听文件无内容，非菜单控件： Expecting value: line 1 column 1 (char 0)
[2025-08-04 23:28:09]  [INFO] 将点击事件加入到事件队列
[2025-08-04 23:28:11]  监听文件无内容，非菜单控件： Expecting value: line 1 column 1 (char 0)
[2025-08-04 23:28:11]  [INFO] 将键盘事件加入到事件队列
[2025-08-04 23:28:11]  [INFO] 将键盘事件加入到事件队列
[2025-08-04 23:28:11]  
收到终止信号 2，正在停止录制...
[2025-08-04 23:28:11]  [INFO] 停止录制会话: 5d9a052e-331f-4681-9c53-5af71315116e
[2025-08-04 23:28:11]  [INFO] 事件处理器已停止
[2025-08-04 23:28:11]  [INFO] 正在保存录制数据到: ./recordings/session_5d9a052e-331f-4681-9c53-5af71315116e.json
[2025-08-04 23:28:11]  [INFO] 录制完成，会话时长: 9.88秒
[2025-08-04 23:28:11]  [INFO] 录制事件统计: 鼠标事件=3, 键盘事件=2
