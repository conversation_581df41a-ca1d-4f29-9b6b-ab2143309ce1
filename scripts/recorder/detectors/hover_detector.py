#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""鼠标悬停检测器 - 修复高亮和信息打印问题"""
import time
import math
import threading
import queue
import sys
import os
import subprocess
import gc
from typing import Optional, Dict, Any, Tuple
# 导入高亮显示模块 - 使用与auto_recording_manager.py相同的方式
try:
    from widget_capture_module import HighlightRenderer
    HIGHLIGHT_AVAILABLE = True
except ImportError:
    HIGHLIGHT_AVAILABLE = False
    HighlightRenderer = None

# 导入UNI模块
try:
    from UNI import UNI
    UNI_AVAILABLE = True
except ImportError:
    UNI_AVAILABLE = False
    UNI = None
from ..utils.logger import _log_widget_recognition_timing


class HoverDetector:
	"""
	鼠标悬停检测器
	负责检测鼠标悬停状态，并在悬停时触发控件识别和高亮显示
	"""

	def __init__(self, widget_analyzer=None, debug: bool = False, event_queue=None, menu_listener=None):
		self.debug = debug
		self.widget_analyzer = widget_analyzer
		self.event_queue = event_queue  # 用于发送悬停事件到录制队列
		self.menu_listener = menu_listener  # 菜单监听器引用，用于协调

		# 悬停检测参数
		self.hover_threshold = 0.5  # 悬停阈值时间（秒）- 用于高亮显示
		self.hover_record_threshold = 3.0  # 悬停录制阈值时间（秒）- 用于录制事件
		self.movement_threshold = 5  # 鼠标移动阈值（像素）

		# 当前状态
		self.current_position = (0, 0)
		self.last_move_time = 0
		self.hover_timer = None
		self.hover_record_timer = None  # 用于录制的悬停计时器
		self.is_hovering = False
		self.hover_recorded = False  # 标记是否已经录制了悬停事件
		self.last_x = -1
		self.last_y = -1

		# 控件捕获任务中断标志
		self.interrupt_flag = threading.Event()  # 用于中断正在进行的控件捕获任务

		# 控件信息缓存
		self.cached_widget_info = None
		self.cached_position = None
		self.cached_timestamp = None
		self.cache_timeout = 5.0  # 缓存超时时间（秒）

		# 高亮显示器 - 使用与auto_recording_manager.py相同的初始化方式
		self.highlight_renderer = None
		if HIGHLIGHT_AVAILABLE:
			try:
				self.highlight_renderer = HighlightRenderer(debug=debug)
				if self.debug:
					print("[DEBUG] 高亮显示器初始化成功", file=sys.stderr)
			except Exception as e:
				print(f"[ERROR] 高亮显示器初始化失败: {e}", file=sys.stderr)
				self.highlight_renderer = None

		# 线程锁
		self.lock = threading.Lock()

		# 运行状态
		self.running = True

		# AT-SPI状态管理
		self.last_window_count = 0
		self.last_atspi_check_time = 0





	def start(self):
		"""启动悬停检测"""
		with self.lock:
			self.running = True
			print("[INFO] 悬停检测器已启动", file=sys.stderr)

	def stop(self):
		"""停止悬停检测"""
		with self.lock:
			self.running = False

			# 取消当前的悬停计时器
			if self.hover_timer:
				self.hover_timer.cancel()
				self.hover_timer = None

			# 取消录制计时器
			if self.hover_record_timer:
				self.hover_record_timer.cancel()
				self.hover_record_timer = None

			# 清除高亮显示
			self._clear_highlight()

			# 清除缓存
			self._clear_cache()

			print("[INFO] 悬停检测器已停止", file=sys.stderr)

	def on_mouse_move(self, x: int, y: int):
		"""处理鼠标移动事件"""
		with self.lock:
			# 如果悬停检测器已停止，不处理事件
			if not self.running:
				return

			current_time = time.time()

			# 检查鼠标是否移动了足够的距离
			distance = math.sqrt((x - self.current_position[0]) ** 2 + (y - self.current_position[1]) ** 2)

			if distance > self.movement_threshold:
				# 鼠标移动距离超过阈值，设置中断标志以停止正在进行的控件捕获任务
				self.interrupt_flag.set()
				if self.debug:
					print(f"[DEBUG] 鼠标移动超过阈值，设置中断标志: 位置({x}, {y}), 距离={distance:.2f}px",
						  file=sys.stderr)

				# 取消之前的悬停计时器
				if self.hover_timer:
					self.hover_timer.cancel()
					self.hover_timer = None

				# 取消录制计时器
				if self.hover_record_timer:
					self.hover_record_timer.cancel()
					self.hover_record_timer = None

				# 清除高亮显示和缓存
				self._clear_highlight()
				self._clear_cache()

				# 更新位置和时间
				self.current_position = (x, y)
				self.last_move_time = current_time
				self.is_hovering = False
				self.hover_recorded = False

				# 启动新的悬停计时器（用于高亮显示）
				self.hover_timer = threading.Timer(
					self.hover_threshold,
					self._on_hover_timeout,
					args=(x, y)
				)
				self.hover_timer.start()

				# 不再启动录制计时器 - 取消悬停事件录制
				# if self.event_queue:
				#     self.hover_record_timer = threading.Timer(
				#         self.hover_record_threshold,
				#         self._on_hover_record_timeout,
				#         args=(x, y)
				#     )
				#     self.hover_record_timer.start()

				# 启动一个定时器，在一段时间后重置中断标志
				interrupt_reset_timer = threading.Timer(
					0.5,  # 500毫秒后重置中断标志
					self._reset_interrupt_flag
				)
				interrupt_reset_timer.start()

	def _reset_interrupt_flag(self):
		"""重置中断标志"""
		with self.lock:
			self.interrupt_flag.clear()

	def restart_self(self):
		"""重启自己"""
		print("正在重启进程...")
		python = sys.executable
		os.execl(python, 'python3', *sys.argv)  # 重启脚本并保留原参数

	def _on_hover_timeout(self, x: int, y: int):
		"""悬停超时回调，触发控件识别和高亮"""
		distance = (x - self.last_x) ** 2 + (y - self.last_y) ** 2
		self.last_x = x
		self.last_y = y

		if distance < 30:
			print(f"[INFO] 鼠标移动距离过小，跳过悬停超时回调", file=sys.stderr)
			return

		try:
			print(f"[INFO] 悬停超时触发: 坐标=({x}, {y})", file=sys.stderr)

			# 先设置悬停状态（在锁内快速完成）
			with self.lock:
				self.is_hovering = True

			# 进行控件识别（在锁外进行，避免阻塞其他事件）
			widget_info = None
			info_text = ""

			if self.widget_analyzer:
				# 检测并修复AT-SPI状态
				self._detect_and_cleanup_atspi_if_needed()

				start_time = time.time()
				print(f"[INFO] 开始控件识别...", file=sys.stderr)

				# 使用新应用强化检测确保识别准确性
				widget_info, info_text = self.widget_analyzer.analyze_widget_at_with_new_app_detection(x, y,
																									   self.interrupt_flag)

				if self.debug:
					print(f"[DEBUG] 控件识别结果: {widget_info}", file=sys.stderr)

				# 检查是否被中断
				if self.interrupt_flag and self.interrupt_flag.is_set():
					print(f"[INFO] 控件识别被中断（悬停超时回调）", file=sys.stderr)
					return

				end_time = time.time()
				recognition_time = end_time - start_time

				# 记录耗时监控日志
				widget_name = widget_info.get('Name', 'Unknown') if widget_info and not widget_info.get(
					'error') else 'Unknown'
				success = widget_info and not widget_info.get('error')
				_log_widget_recognition_timing(
					"analyze_widget_at_with_new_app_detection",
					start_time, end_time, x, y,
					success, widget_name, info_text if not success else ""
				)

				print(f"[INFO] 控件识别完成，耗时: {recognition_time:.3f}秒", file=sys.stderr)
				if recognition_time > 3:
					print(f"[WARNING] 控件识别耗时过长: {recognition_time:.3f}秒,重建WidgetAnalyzer", file=sys.stderr)
					self.restart_self()

				# 检查是否被中断
				if self.interrupt_flag and self.interrupt_flag.is_set():
					print(f"[INFO] 控件识别被中断（识别完成后）", file=sys.stderr)
					return

				# 验证控件信息并强制打印
				if widget_info and not widget_info.get('error'):
					# 强制打印控件信息 - 解决不打印的问题
					print("\n" + "=" * 60, file=sys.stderr)
					print(f"[SUCCESS] 成功识别控件信息:", file=sys.stderr)
					print(f"  名称: {widget_info.get('Name', 'Unknown')}", file=sys.stderr)
					print(f"  类型: {widget_info.get('Type', 'Unknown')}", file=sys.stderr)
					print(f"  进程: {widget_info.get('ProcessName', 'Unknown')}", file=sys.stderr)
					coords = widget_info.get('Coords', {})
					print(f"  位置: x={coords.get('x', 0)}, y={coords.get('y', 0)}, "
						  f"宽={coords.get('width', 0)}, 高={coords.get('height', 0)}", file=sys.stderr)
					print("=" * 60 + "\n", file=sys.stderr)

					# 在锁内快速更新缓存
					with self.lock:
						self.cached_widget_info = widget_info
						self.cached_position = (x, y)
						self.cached_timestamp = time.time()

					# 高亮显示控件（异步进行，避免阻塞）
					self._highlight_widget_async(widget_info)

					widget_name = widget_info.get('Name', 'Unknown')
					process_name = widget_info.get('ProcessName', 'Unknown')
					print(f"[INFO] 🎯 悬停识别成功并缓存: {widget_name} (进程: {process_name})", file=sys.stderr)
				else:
					print(f"[INFO] 悬停识别失败: {info_text}", file=sys.stderr)
					with self.lock:
						self._clear_cache()

			else:
				print("[WARNING] 未初始化控件分析器，无法识别控件", file=sys.stderr)

		except Exception as e:
			print(f"[ERROR] 悬停处理时发生错误: {e}", file=sys.stderr)
			if self.debug:
				import traceback
				traceback.print_exc(file=sys.stderr)

	def _on_hover_record_timeout(self, x: int, y: int):
		"""悬停录制超时回调 - 已禁用悬停事件录制"""
		# 不再生成悬停事件，只记录调试信息
		if self.debug:
			print(f"[DEBUG] 悬停录制触发但已禁用: 坐标=({x}, {y})", file=sys.stderr)
		return  # 直接返回，不生成悬停事件

	def _highlight_widget(self, widget_info: Dict[str, Any]):
		"""高亮显示控件 - 使用与auto_recording_manager.py相同的方式"""
		if not self.highlight_renderer:
			return

		try:
			# 获取控件坐标信息
			coords = widget_info.get('Coords')
			if coords:
				self.highlight_renderer.highlight_widget(
					coords['x'],
					coords['y'],
					coords['width'],
					coords['height'],
					widget_info
				)
				if self.debug:
					print(f"[DEBUG] 高亮控件: x={coords['x']}, y={coords['y']}, w={coords['width']}, h={coords['height']}", file=sys.stderr)
		except Exception as e:
			if self.debug:
				print(f"[DEBUG] 高亮显示控件时发生错误: {e}", file=sys.stderr)

	def _highlight_widget_async(self, widget_info: Dict[str, Any]):
		"""异步高亮显示控件 - 使用与auto_recording_manager.py相同的方式"""

		def highlight_worker():
			try:
				self._highlight_widget(widget_info)
			except Exception as e:
				if self.debug:
					print(f"[DEBUG] 异步高亮显示失败: {e}", file=sys.stderr)

		# 在新线程中执行高亮显示
		highlight_thread = threading.Thread(target=highlight_worker, daemon=True)
		highlight_thread.start()

	def _clear_highlight(self):
		"""清除高亮显示 - 使用与auto_recording_manager.py相同的方式"""
		if self.highlight_renderer:
			try:
				self.highlight_renderer.clear_highlight()
			except Exception as e:
				if self.debug:
					print(f"[DEBUG] 清除高亮时发生错误: {e}", file=sys.stderr)

	def _detect_and_cleanup_atspi_if_needed(self):
		"""检测窗口关闭并清理AT-SPI状态"""
		try:
			current_time = time.time()

			# 限制检查频率，避免过度消耗资源
			if current_time - self.last_atspi_check_time < 0.5:
				return

			self.last_atspi_check_time = current_time

			# 获取当前可见窗口数量
			result = subprocess.run(['xdotool', 'search', '--onlyvisible', '--name', '.*'],
									capture_output=True, text=True, timeout=0.5)

			current_window_count = 0
			if result.returncode == 0 and result.stdout.strip():
				current_window_count = len(result.stdout.strip().splitlines())
				if self.debug:
					print(f"[DEBUG] 当前可见窗口数: {current_window_count}", file=sys.stderr)

			# 检测窗口数量是否减少（可能有窗口关闭）
			if self.last_window_count > 0 and current_window_count < self.last_window_count:
				window_closed_count = self.last_window_count - current_window_count
				if self.debug:
					print(f"[DEBUG] 🚨 检测到 {window_closed_count} 个窗口关闭，执行深度AT-SPI恢复", file=sys.stderr)

				# 深度AT-SPI状态恢复
				start_time = time.time()
				self._perform_deep_atspi_recovery()
				end_time = time.time()
				print(f"[INFO] _perform_deep_atspi_recovery,耗时：{end_time - start_time}", file=sys.stderr)
				_log_widget_recognition_timing(
					"deep_atspi_recovery",
					start_time, end_time, "", "",
					True, "", f"{window_closed_count} windows closed"
				)

			self.last_window_count = current_window_count

		except Exception as e:
			if self.debug:
				print(f"[DEBUG] AT-SPI状态检测失败: {e}", file=sys.stderr)

	def _perform_deep_atspi_recovery(self):
		"""执行深度AT-SPI状态恢复"""
		try:
			if self.debug:
				print(f"[DEBUG] 🔧 开始深度AT-SPI状态恢复...", file=sys.stderr)

			recovery_steps = []

			# 步骤1: 清理本地缓存
			try:
				if hasattr(self.widget_analyzer, '_clear_cache'):
					self.widget_analyzer._clear_cache()
				self.cached_widget_info = None
				self.cached_position = None
				self.cached_timestamp = None
				recovery_steps.append("✅ 本地缓存已清理")
			except Exception as e:
				recovery_steps.append(f"❌ 本地缓存清理失败: {e}")

			# 步骤2: 强制刷新桌面对象
			try:
				if hasattr(self.widget_analyzer, 'uni') and self.widget_analyzer.uni:
					# 清除UNI模块的桌面缓存
					if hasattr(self.widget_analyzer.uni, 'desktop_cache'):
						self.widget_analyzer.uni.desktop_cache = {}  # 重置为空字典
					if hasattr(self.widget_analyzer.uni, '_cached_desktop'):
						self.widget_analyzer.uni._cached_desktop = None
					recovery_steps.append("✅ UNI桌面缓存已重置")
			except Exception as e:
				recovery_steps.append(f"❌ UNI桌面重置失败: {e}")

			# 步骤3: 验证AT-SPI连接状态
			try:
				import pyatspi
				desktop = pyatspi.Registry.getDesktop(0)
				app_count = desktop.childCount
				recovery_steps.append(f"✅ AT-SPI连接正常，应用数: {app_count}")
			except Exception as e:
				recovery_steps.append(f"⚠️ AT-SPI连接异常: {e}")

				# 尝试重新初始化AT-SPI连接
				try:
					import pyatspi
					pyatspi.Registry.start()  # 重启AT-SPI注册表
					recovery_steps.append("✅ AT-SPI注册表已重启")
				except Exception as restart_e:
					recovery_steps.append(f"❌ AT-SPI重启失败: {restart_e}")

			# 步骤4: 清理可能的僵尸对象引用
			try:
				gc.collect()  # 强制垃圾回收，清理悬空引用
				recovery_steps.append("✅ 垃圾回收已执行")
			except Exception as e:
				recovery_steps.append(f"❌ 垃圾回收失败: {e}")

			# 输出恢复结果
			if self.debug:
				print(f"[DEBUG] 🎯 AT-SPI恢复完成，执行步骤:", file=sys.stderr)
				for step in recovery_steps:
					print(f"[DEBUG]   {step}", file=sys.stderr)

			# 统计恢复成功率
			success_count = sum(1 for step in recovery_steps if step.startswith("✅"))
			total_count = len(recovery_steps)
			success_rate = (success_count / total_count) * 100 if total_count > 0 else 0

			if self.debug:
				print(f"[DEBUG] 📊 恢复成功率: {success_rate:.1f}% ({success_count}/{total_count})", file=sys.stderr)

		except Exception as e:
			if self.debug:
				print(f"[DEBUG] ❌ 深度AT-SPI恢复失败: {e}", file=sys.stderr)

	def get_cached_widget_info(self, x: int, y: int, tolerance: int = 15) -> Optional[Dict[str, Any]]:
		"""获取缓存的控件信息（智能匹配悬停控件与点击位置）"""
		with self.lock:
			# 检查缓存是否存在和有效
			if not self.cached_widget_info or not self.cached_position or not self.cached_timestamp:
				if self.debug:
					print(f"[DEBUG] 无缓存控件信息可用", file=sys.stderr)
				return None

			# 检查缓存是否过期
			if time.time() - self.cached_timestamp > self.cache_timeout:
				if self.debug:
					print(f"[DEBUG] 缓存已过期，清理缓存", file=sys.stderr)
				self._clear_cache()
				return None

			# 获取缓存控件的坐标信息
			coords = self.cached_widget_info.get('Coords', {})
			cached_x, cached_y = self.cached_position

			# 策略1: 检查点击位置是否在控件边界内
			if coords and isinstance(coords, dict):
				widget_x = coords.get('x', 0)
				widget_y = coords.get('y', 0)
				widget_width = coords.get('width', 0)
				widget_height = coords.get('height', 0)

				if (widget_x <= x <= widget_x + widget_width and
					widget_y <= y <= widget_y + widget_height):
					if self.debug:
						widget_name = self.cached_widget_info.get('Name', 'Unknown')
						print(f"[DEBUG] ✅ 点击位置在缓存控件边界内，使用悬停控件: {widget_name}", file=sys.stderr)
					return self.cached_widget_info

			# 策略2: 检查点击位置是否在悬停位置的容差范围内
			distance = math.sqrt((x - cached_x) ** 2 + (y - cached_y) ** 2)

			if distance <= tolerance:
				if self.debug:
					widget_name = self.cached_widget_info.get('Name', 'Unknown')
					print(f"[DEBUG] ✅ 点击位置在容差范围内({distance:.1f}像素)，使用悬停控件: {widget_name}",
						  file=sys.stderr)
				return self.cached_widget_info
			else:
				if self.debug:
					print(f"[DEBUG] ❌ 点击位置距离悬停位置太远({distance:.1f}像素 > {tolerance})，重新识别控件",
						  file=sys.stderr)
				return None

	def _clear_cache(self):
		"""清理控件信息缓存"""
		self.cached_widget_info = None
		self.cached_position = None
		self.cached_timestamp = None

	def cleanup(self):
		"""清理资源"""
		with self.lock:
			if self.hover_timer:
				self.hover_timer.cancel()
				self.hover_timer = None

			self._clear_highlight()

			if self.highlight_renderer:
				try:
					# 如果高亮渲染器有cleanup方法，调用它
					if hasattr(self.highlight_renderer, 'cleanup'):
						self.highlight_renderer.cleanup()
					print("[INFO] 高亮渲染器已清理", file=sys.stderr)
				except Exception as e:
					print(f"[ERROR] 清理高亮渲染器时发生错误: {e}", file=sys.stderr)
