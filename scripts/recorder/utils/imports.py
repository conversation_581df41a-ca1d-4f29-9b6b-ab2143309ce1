#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""第三方库导入管理模块"""
import sys
import os

# 添加项目根目录到Python路径
script_dir = os.path.dirname(os.path.abspath(__file__))
recorder_dir = os.path.dirname(script_dir)
project_root = os.path.dirname(recorder_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入事件监听库
try:
    from pynput import mouse, keyboard
    PYNPUT_AVAILABLE = True
    # 导出mouse和keyboard供其他模块使用
    pynput_mouse = mouse
    pynput_keyboard = keyboard
except ImportError:
    PYNPUT_AVAILABLE = False
    pynput_mouse = None
    pynput_keyboard = None
    print("[ERROR] pynput库不可用，无法进行全局事件监听", file=sys.stderr)


# 导入UNI模块
try:
    # 计算绝对路径，避免工作目录差异
    current_file_dir = os.path.dirname(os.path.abspath(__file__))  # .../scripts/recorder/utils
    recorder_utils_dir = current_file_dir  # .../scripts/recorder/utils
    recorder_dir = os.path.dirname(recorder_utils_dir)  # .../scripts/recorder
    scripts_dir = os.path.dirname(recorder_dir)  # .../scripts
    project_root_dir = os.path.dirname(scripts_dir)  # .../kylin-robot-ide

    UNI_PATHS = [
        scripts_dir,  # 主要路径：/path/to/scripts
        os.path.join(project_root_dir, "extensions/KylinRobot-v2/common"),  # 备用路径1
        scripts_dir,  # 当前scripts目录
        os.path.join(project_root_dir, "kylinrobot-ide-x64-remote-display-enhanced/resources/app/scripts"),  # 备用路径2
        os.path.join(project_root_dir, "kylinrobot-ide-arm64-remote-display-enhanced/resources/app/scripts")  # 备用路径3
    ]

    UNI_AVAILABLE = False
    UNI = None
    for path in UNI_PATHS:
        try:
            if path not in sys.path:
                sys.path.insert(0, path)
            from UNI import UNI as _UNI
            UNI = _UNI
            UNI_AVAILABLE = True
            print(f"[INFO] UNI模块从路径加载成功: {path}", file=sys.stderr)
            break
        except ImportError:
            continue

    if not UNI_AVAILABLE:
        print("[ERROR] UNI模块导入失败，尝试的路径:", file=sys.stderr)
        for path in UNI_PATHS:
            print(f"  - {path}", file=sys.stderr)

except Exception as e:
    UNI_AVAILABLE = False
    UNI = None
    print(f"[ERROR] UNI模块导入异常: {e}", file=sys.stderr)


# 导入高亮显示模块
try:
    from widget_capture_module import HighlightRenderer
    HIGHLIGHT_AVAILABLE = True
    print("[INFO] 高亮显示模块加载成功", file=sys.stderr)
except ImportError:
    HIGHLIGHT_AVAILABLE = False
    print("[WARNING] 高亮显示模块不可用，将跳过高亮功能", file=sys.stderr)
except Exception as e:
    HIGHLIGHT_AVAILABLE = False
    print(f"[ERROR] 高亮显示模块导入异常: {e}", file=sys.stderr)


# 导入locator生成器
try:
    from .locator_generator import LocatorGenerator
    LOCATOR_GENERATOR_AVAILABLE = True
    print("[INFO] Locator生成器加载成功", file=sys.stderr)
except ImportError as e:
    LOCATOR_GENERATOR_AVAILABLE = False
    print(f"[WARNING] Locator生成器不可用: {e}", file=sys.stderr)
except Exception as e:
    LOCATOR_GENERATOR_AVAILABLE = False
    print(f"[ERROR] Locator生成器导入异常: {e}", file=sys.stderr)
