#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断菜单监听器问题

作者: GAT开发团队
日期: 2025年1月4日
"""

import os
import sys
import subprocess
import time
import signal

def check_pause_file_status():
    """检查暂停文件状态"""
    print("🔍 检查暂停文件状态")
    print("="*40)
    
    pause_file = "/tmp/.pause_menu_listener.txt"
    
    print(f"📋 暂停文件路径: {pause_file}")
    
    if os.path.exists(pause_file):
        print(f"   ⚠️  暂停文件存在！这可能导致菜单监听器被暂停")
        
        try:
            with open(pause_file, 'r') as f:
                content = f.read()
            print(f"   📄 文件内容: '{content}'")
            
            # 删除暂停文件
            os.remove(pause_file)
            print(f"   🧹 已删除暂停文件")
        
        except Exception as e:
            print(f"   ❌ 处理暂停文件失败: {e}")
    
    else:
        print(f"   ✅ 暂停文件不存在（正常）")

def test_menu_listener_events():
    """测试菜单监听器事件处理"""
    print(f"\n🔍 测试菜单监听器事件处理")
    print("="*50)
    
    print("📋 启动菜单监听器进行事件测试...")
    
    try:
        # 确保暂停文件不存在
        pause_file = "/tmp/.pause_menu_listener.txt"
        if os.path.exists(pause_file):
            os.remove(pause_file)
            print(f"   🧹 清理暂停文件")
        
        # 启动菜单监听器
        process = subprocess.Popen(
            ['python3', 'listenHF.py'],
            cwd='/home/<USER>/kylin-robot-ide/scripts',
            env=dict(os.environ, DISPLAY=':0'),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print(f"   🚀 菜单监听器已启动，PID: {process.pid}")
        
        # 等待启动
        time.sleep(2)
        
        # 检查进程状态
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            print(f"   ❌ 菜单监听器启动失败")
            print(f"   stdout: {stdout}")
            print(f"   stderr: {stderr}")
            return
        
        print(f"   ✅ 菜单监听器正常运行")
        
        # 监控10秒，看是否有事件输出
        print(f"   📋 监控10秒，观察事件处理...")
        
        start_time = time.time()
        event_detected = False
        
        while time.time() - start_time < 10:
            if process.poll() is not None:
                break
            
            # 读取输出
            try:
                import select
                ready, _, _ = select.select([process.stdout, process.stderr], [], [], 0.1)
                
                if process.stdout in ready:
                    line = process.stdout.readline()
                    if line:
                        line = line.strip()
                        print(f"   📥 stdout: {line}")
                        if 'menu detected' in line.lower() or 'popup' in line.lower():
                            event_detected = True
                
                if process.stderr in ready:
                    line = process.stderr.readline()
                    if line:
                        line = line.strip()
                        print(f"   📥 stderr: {line}")
            
            except Exception:
                break
            
            time.sleep(0.1)
        
        # 终止进程
        if process.poll() is None:
            print(f"   ⏰ 监控结束，终止进程...")
            process.terminate()
            try:
                process.wait(timeout=3)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
        
        print(f"   📊 事件检测: {'有事件' if event_detected else '无事件'}")
        
        # 检查菜单记录文件
        menu_files = ["/tmp/.recordmenu.txt", "/tmp/.recordmenu1.txt"]
        
        for menu_file in menu_files:
            if os.path.exists(menu_file):
                try:
                    with open(menu_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    if content.strip():
                        print(f"   ✅ {menu_file} 有内容 ({len(content)}字符)")
                    else:
                        print(f"   📝 {menu_file} 为空")
                
                except Exception as e:
                    print(f"   ❌ 读取 {menu_file} 失败: {e}")
            else:
                print(f"   📄 {menu_file} 不存在")
    
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")

def compare_manual_vs_auto_startup():
    """对比手动启动vs自动启动"""
    print(f"\n🔍 对比手动启动vs自动启动")
    print("="*50)
    
    scenarios = [
        {
            'name': '手动启动（原始版本）',
            'method': 'manual',
            'description': '直接运行listenHF.py，无协调机制'
        },
        {
            'name': '自动启动（录制器版本）',
            'method': 'auto',
            'description': '通过录制器启动，有协调机制'
        }
    ]
    
    for scenario in scenarios:
        print(f"\n🔵 {scenario['name']}")
        print(f"   描述: {scenario['description']}")
        
        if scenario['method'] == 'manual':
            # 手动启动测试
            print(f"   📋 手动启动测试...")
            
            # 创建一个临时的listenHF.py副本，移除协调机制
            create_original_listener()
            
            try:
                process = subprocess.Popen(
                    ['python3', 'listenHF_original.py'],
                    cwd='/home/<USER>/kylin-robot-ide/scripts',
                    env=dict(os.environ, DISPLAY=':0'),
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                print(f"      🚀 进程启动，PID: {process.pid}")
                time.sleep(3)
                
                if process.poll() is None:
                    print(f"      ✅ 正常运行")
                    process.terminate()
                    process.wait()
                else:
                    print(f"      ❌ 启动失败")
            
            except Exception as e:
                print(f"      ❌ 测试失败: {e}")
        
        else:
            # 自动启动测试
            print(f"   📋 自动启动测试...")
            
            try:
                process = subprocess.Popen(
                    ['python3', 'scripts/recorder/run_recorder.py', '--debug', '--duration', '3'],
                    cwd='/home/<USER>/kylin-robot-ide',
                    env=dict(os.environ, DISPLAY=':0'),
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                print(f"      🚀 录制器启动，PID: {process.pid}")
                
                # 监控输出
                menu_started = False
                start_time = time.time()
                
                while time.time() - start_time < 4:
                    if process.poll() is not None:
                        break
                    
                    try:
                        import select
                        ready, _, _ = select.select([process.stdout, process.stderr], [], [], 0.1)
                        
                        if process.stderr in ready:
                            line = process.stderr.readline()
                            if line and '菜单监听器已启动' in line:
                                menu_started = True
                                print(f"      ✅ 菜单监听器启动成功")
                                break
                    
                    except Exception:
                        break
                    
                    time.sleep(0.1)
                
                if not menu_started:
                    print(f"      ❌ 菜单监听器启动失败")
                
                # 终止进程
                if process.poll() is None:
                    process.terminate()
                    try:
                        process.wait(timeout=2)
                    except subprocess.TimeoutExpired:
                        process.kill()
                        process.wait()
            
            except Exception as e:
                print(f"      ❌ 测试失败: {e}")

def create_original_listener():
    """创建原始版本的菜单监听器（无协调机制）"""
    try:
        # 读取当前版本
        with open('/home/<USER>/kylin-robot-ide/scripts/listenHF.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 移除协调机制相关代码
        lines = content.split('\n')
        filtered_lines = []
        
        skip_coordination = False
        
        for line in lines:
            # 跳过协调机制相关代码
            if 'should_pause' in line or 'pause_file' in line:
                continue
            if '检查是否暂停' in line:
                skip_coordination = True
                continue
            if skip_coordination and 'return  # 暂停期间跳过事件处理' in line:
                skip_coordination = False
                continue
            if skip_coordination:
                continue
            
            filtered_lines.append(line)
        
        # 写入原始版本
        with open('/home/<USER>/kylin-robot-ide/scripts/listenHF_original.py', 'w', encoding='utf-8') as f:
            f.write('\n'.join(filtered_lines))
        
        print(f"   📝 创建原始版本菜单监听器成功")
    
    except Exception as e:
        print(f"   ❌ 创建原始版本失败: {e}")

def main():
    print("🔍 GAT菜单监听器问题诊断")
    print("="*50)
    
    print("🎯 诊断目标:")
    print("• 检查暂停文件状态")
    print("• 测试菜单监听器事件处理")
    print("• 对比手动启动vs自动启动")
    print("• 找出菜单内容不写入的原因")
    
    # 检查暂停文件状态
    check_pause_file_status()
    
    # 测试菜单监听器事件处理
    test_menu_listener_events()
    
    # 对比手动启动vs自动启动
    compare_manual_vs_auto_startup()
    
    print(f"\n🎉 诊断完成！")
    print("如果发现问题，请根据诊断结果进行修复")

if __name__ == "__main__":
    main()
