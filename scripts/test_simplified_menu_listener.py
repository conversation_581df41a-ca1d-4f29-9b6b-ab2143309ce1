#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化后的菜单监听器方案

作者: GAT开发团队
日期: 2025年1月4日
"""

import os
import sys
import subprocess
import time
import signal

def test_global_menu_listener():
    """测试全局菜单监听器"""
    print("🔍 测试全局菜单监听器")
    print("="*50)
    
    print("📋 启动录制器，观察菜单监听器状态...")
    
    try:
        # 启动录制器
        process = subprocess.Popen(
            ['python3', 'scripts/recorder/run_recorder.py', '--debug', '--duration', '8'],
            cwd='/home/<USER>/kylin-robot-ide',
            env=dict(os.environ, DISPLAY=':0'),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print(f"   🚀 录制器已启动，PID: {process.pid}")
        
        # 监控输出
        start_time = time.time()
        global_menu_started = False
        global_menu_pid = None
        global_menu_stopped = False
        
        while time.time() - start_time < 6:
            if process.poll() is not None:
                break
            
            try:
                import select
                ready, _, _ = select.select([process.stdout, process.stderr], [], [], 0.1)
                
                if process.stderr in ready:
                    line = process.stderr.readline()
                    if line:
                        line = line.strip()
                        
                        # 检测全局菜单监听器状态
                        if '全局菜单监听器已启动' in line:
                            global_menu_started = True
                            # 提取PID
                            if 'PID:' in line:
                                try:
                                    global_menu_pid = int(line.split('PID:')[1].strip())
                                    print(f"   ✅ 全局菜单监听器启动成功，PID: {global_menu_pid}")
                                except:
                                    pass
                        elif '全局菜单监听器已停止' in line:
                            global_menu_stopped = True
                            print(f"   ✅ 全局菜单监听器已停止")
            
            except Exception:
                break
            
            time.sleep(0.1)
        
        # 等待进程结束
        if process.poll() is None:
            print(f"   ⏰ 等待录制器结束...")
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.terminate()
                process.wait()
        
        execution_time = time.time() - start_time
        
        print(f"\n📊 测试结果:")
        print(f"   执行时间: {execution_time:.3f}秒")
        print(f"   全局菜单启动: {'✅ 成功' if global_menu_started else '❌ 失败'}")
        print(f"   菜单监听PID: {global_menu_pid if global_menu_pid else '未获取'}")
        print(f"   全局菜单停止: {'✅ 成功' if global_menu_stopped else '❌ 失败'}")
        
        # 检查菜单记录文件
        menu_files = ["/tmp/.recordmenu.txt", "/tmp/.recordmenu1.txt"]
        
        for menu_file in menu_files:
            if os.path.exists(menu_file):
                try:
                    with open(menu_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    if content.strip():
                        print(f"   📄 {menu_file}: 有内容 ({len(content)}字符)")
                        print(f"      内容预览: {content[:100]}...")
                    else:
                        print(f"   📄 {menu_file}: 为空")
                
                except Exception as e:
                    print(f"   ❌ 读取 {menu_file} 失败: {e}")
            else:
                print(f"   📄 {menu_file}: 不存在")
        
        # 检查菜单监听器进程是否还在运行
        if global_menu_pid:
            try:
                result = subprocess.run(
                    ['ps', '-p', str(global_menu_pid)],
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 0:
                    print(f"   ⚠️  菜单监听器进程 {global_menu_pid} 仍在运行")
                else:
                    print(f"   ✅ 菜单监听器进程 {global_menu_pid} 已正确停止")
            
            except Exception:
                pass
        
        return global_menu_started and global_menu_stopped
    
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def test_menu_file_persistence():
    """测试菜单文件持久性"""
    print(f"\n🔍 测试菜单文件持久性")
    print("="*40)
    
    print("📋 手动启动菜单监听器，生成菜单内容...")
    
    try:
        # 手动启动菜单监听器
        menu_process = subprocess.Popen(
            ['python3', 'listenHF.py'],
            cwd='/home/<USER>/kylin-robot-ide/scripts',
            env=dict(os.environ, DISPLAY=':0'),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print(f"   🚀 手动菜单监听器已启动，PID: {menu_process.pid}")
        
        # 等待5秒，让它监听事件
        print(f"   ⏰ 等待5秒，监听菜单事件...")
        time.sleep(5)
        
        # 终止菜单监听器
        if menu_process.poll() is None:
            menu_process.terminate()
            try:
                menu_process.wait(timeout=3)
            except subprocess.TimeoutExpired:
                menu_process.kill()
                menu_process.wait()
        
        print(f"   ✅ 手动菜单监听器已停止")
        
        # 检查菜单文件
        menu_files = ["/tmp/.recordmenu.txt", "/tmp/.recordmenu1.txt"]
        
        for menu_file in menu_files:
            if os.path.exists(menu_file):
                try:
                    with open(menu_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    if content.strip():
                        print(f"   📄 {menu_file}: 有内容 ({len(content)}字符)")
                        
                        # 现在启动录制器，看是否保留内容
                        print(f"   🔍 启动录制器，检查文件是否被保留...")
                        
                        recorder_process = subprocess.Popen(
                            ['python3', 'scripts/recorder/run_recorder.py', '--debug', '--duration', '2'],
                            cwd='/home/<USER>/kylin-robot-ide',
                            env=dict(os.environ, DISPLAY=':0'),
                            stdout=subprocess.DEVNULL,
                            stderr=subprocess.DEVNULL
                        )
                        
                        # 等待录制器完成
                        recorder_process.wait()
                        
                        # 再次检查文件
                        if os.path.exists(menu_file):
                            with open(menu_file, 'r', encoding='utf-8') as f:
                                new_content = f.read()
                            
                            if new_content.strip():
                                print(f"   ✅ 录制器结束后文件仍有内容 ({len(new_content)}字符)")
                                if len(new_content) >= len(content):
                                    print(f"   🎉 内容被保留或增加了")
                                else:
                                    print(f"   ⚠️  内容减少了")
                            else:
                                print(f"   ❌ 录制器结束后文件被清空")
                        else:
                            print(f"   ❌ 录制器结束后文件被删除")
                        
                        return True
                    else:
                        print(f"   📄 {menu_file}: 为空")
                
                except Exception as e:
                    print(f"   ❌ 处理 {menu_file} 失败: {e}")
            else:
                print(f"   📄 {menu_file}: 不存在")
        
        print(f"   ℹ️  没有菜单内容生成，可能需要手动触发菜单事件")
        return False
    
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False

def main():
    print("🔍 GAT简化菜单监听器方案测试")
    print("="*60)
    
    print("🎯 测试目标:")
    print("• 验证全局菜单监听器能正确启动和停止")
    print("• 检查菜单文件是否被正确保留")
    print("• 确认简化方案的有效性")
    
    # 测试全局菜单监听器
    global_test_passed = test_global_menu_listener()
    
    # 测试菜单文件持久性
    persistence_test_passed = test_menu_file_persistence()
    
    print(f"\n🎉 测试总结:")
    print(f"   全局菜单监听器: {'✅ 通过' if global_test_passed else '❌ 失败'}")
    print(f"   菜单文件持久性: {'✅ 通过' if persistence_test_passed else '❌ 失败'}")
    
    if global_test_passed:
        print(f"\n🎊 简化方案成功！")
        print("• 全局菜单监听器由主进程直接管理")
        print("• 不需要复杂的协调机制")
        print("• 菜单内容可以正常记录和保留")
        print("• 解决了之前的启动和清空问题")
    else:
        print(f"\n⚠️  简化方案需要进一步调试")

if __name__ == "__main__":
    main()
