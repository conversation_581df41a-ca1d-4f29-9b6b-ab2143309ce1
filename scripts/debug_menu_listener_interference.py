#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试菜单监听器对中断机制的干扰

作者: GAT开发团队
日期: 2025年1月4日
"""

import os
import sys
import subprocess
import time
import threading

def analyze_menu_listener_interference():
    """分析菜单监听器对中断机制的干扰"""
    print("🔍 分析菜单监听器对中断机制的干扰")
    print("="*60)
    
    print("📋 可能的干扰原因:")
    print("1. AT-SPI事件监听冲突")
    print("2. 鼠标事件处理竞争")
    print("3. 桌面对象获取阻塞")
    print("4. GLib主循环干扰")
    print("5. 进程间通信阻塞")
    
    # 检查listenHF.py的事件监听机制
    print(f"\n🔍 检查listenHF.py的事件监听机制:")
    
    try:
        with open('/home/<USER>/kylin-robot-ide/scripts/listenHF.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 分析关键代码
        if 'pyatspi' in content:
            print("   ✅ 使用pyatspi进行AT-SPI监听")
        
        if 'GLib.MainLoop' in content:
            print("   ⚠️  使用GLib.MainLoop主循环")
        
        if 'mouse' in content.lower():
            print("   ⚠️  可能监听鼠标事件")
        
        if 'desktop' in content.lower():
            print("   ⚠️  访问桌面对象")
        
        # 检查是否有阻塞操作
        blocking_patterns = ['time.sleep', 'while True', 'for app in desktop']
        for pattern in blocking_patterns:
            if pattern in content:
                print(f"   ⚠️  发现可能的阻塞操作: {pattern}")
    
    except Exception as e:
        print(f"   ❌ 无法分析listenHF.py: {e}")

def test_with_and_without_menu_listener():
    """测试有无菜单监听器的中断机制差异"""
    print(f"\n🔍 测试有无菜单监听器的中断机制差异")
    print("="*50)
    
    scenarios = [
        {
            'name': '无菜单监听器',
            'setup': lambda: kill_menu_listener(),
            'description': '确保没有listenHF.py进程运行'
        },
        {
            'name': '有菜单监听器',
            'setup': lambda: start_menu_listener(),
            'description': '启动listenHF.py进程'
        }
    ]
    
    for scenario in scenarios:
        print(f"\n🔵 {scenario['name']} ({scenario['description']})")
        
        try:
            # 设置环境
            scenario['setup']()
            time.sleep(1)  # 等待环境稳定
            
            # 检查listenHF.py进程状态
            result = subprocess.run(
                ['pgrep', '-f', 'listenHF.py'],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                pids = result.stdout.strip().split('\n')
                print(f"   📋 listenHF.py进程: {len(pids)}个 (PIDs: {', '.join(pids)})")
            else:
                print(f"   📋 listenHF.py进程: 0个")
            
            # 启动录制器进行测试
            print(f"   🚀 启动录制器测试...")
            
            process = subprocess.Popen(
                ['python3', 'recorder/run_recorder.py', '--debug', '--duration', '5'],
                cwd='/home/<USER>/kylin-robot-ide/scripts',
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 监控3秒
            start_time = time.time()
            interrupt_detected = False
            
            while time.time() - start_time < 3:
                if process.poll() is not None:
                    break
                
                try:
                    import select
                    ready, _, _ = select.select([process.stdout, process.stderr], [], [], 0.1)
                    
                    if process.stderr in ready:
                        line = process.stderr.readline()
                        if line and '控件识别被中断' in line:
                            interrupt_detected = True
                            print(f"   ✅ 检测到中断机制工作")
                            break
                
                except Exception:
                    break
                
                time.sleep(0.1)
            
            # 终止进程
            if process.poll() is None:
                process.terminate()
                try:
                    process.wait(timeout=2)
                except subprocess.TimeoutExpired:
                    process.kill()
                    process.wait()
            
            print(f"   📊 中断机制: {'正常' if interrupt_detected else '失效'}")
            
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")

def kill_menu_listener():
    """终止菜单监听器进程"""
    try:
        subprocess.run(['pkill', '-f', 'listenHF.py'], capture_output=True)
        time.sleep(0.5)
    except:
        pass

def start_menu_listener():
    """启动菜单监听器进程"""
    try:
        # 先终止现有进程
        kill_menu_listener()
        
        # 启动新进程
        subprocess.Popen(
            ['python3', 'listenHF.py'],
            cwd='/home/<USER>/kylin-robot-ide/scripts',
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL
        )
        time.sleep(1)
    except Exception as e:
        print(f"启动菜单监听器失败: {e}")

def analyze_atspi_conflict():
    """分析AT-SPI冲突"""
    print(f"\n🔍 分析AT-SPI冲突")
    print("="*30)
    
    # 检查AT-SPI服务状态
    try:
        result = subprocess.run(
            ['systemctl', '--user', 'status', 'at-spi-dbus-bus'],
            capture_output=True,
            text=True
        )
        
        if 'active (running)' in result.stdout:
            print("   ✅ AT-SPI服务正常运行")
        else:
            print("   ⚠️  AT-SPI服务状态异常")
    
    except Exception as e:
        print(f"   ❌ 无法检查AT-SPI服务: {e}")
    
    # 检查AT-SPI连接数
    try:
        result = subprocess.run(
            ['ps', 'aux'],
            capture_output=True,
            text=True
        )
        
        atspi_processes = [line for line in result.stdout.split('\n') if 'at-spi' in line.lower()]
        print(f"   📋 AT-SPI相关进程: {len(atspi_processes)}个")
        
        python_atspi = [line for line in result.stdout.split('\n') if 'python' in line and 'atspi' in line.lower()]
        print(f"   📋 Python AT-SPI进程: {len(python_atspi)}个")
    
    except Exception as e:
        print(f"   ❌ 无法检查AT-SPI进程: {e}")

def suggest_solutions():
    """建议解决方案"""
    print(f"\n💡 解决方案建议:")
    print("="*30)
    
    solutions = [
        "1. 优化菜单监听器的事件处理机制",
        "2. 使用独立的AT-SPI连接避免冲突",
        "3. 实现事件优先级机制",
        "4. 添加进程间通信协调",
        "5. 优化GLib主循环的使用"
    ]
    
    for solution in solutions:
        print(f"   {solution}")
    
    print(f"\n🔧 具体修复策略:")
    strategies = [
        "• 在悬停检测期间暂停菜单监听",
        "• 使用信号量协调两个进程的AT-SPI访问",
        "• 实现菜单监听器的快速中断机制",
        "• 优化桌面对象获取的并发控制",
        "• 添加进程间的状态同步"
    ]
    
    for strategy in strategies:
        print(f"   {strategy}")

def main():
    print("🔍 GAT菜单监听器干扰中断机制分析")
    print("="*60)
    
    # 分析干扰原因
    analyze_menu_listener_interference()
    
    # 分析AT-SPI冲突
    analyze_atspi_conflict()
    
    # 测试有无菜单监听器的差异
    test_with_and_without_menu_listener()
    
    # 建议解决方案
    suggest_solutions()
    
    print(f"\n🎯 结论:")
    print("菜单监听器确实会干扰中断机制，主要原因可能是:")
    print("1. AT-SPI桌面对象访问冲突")
    print("2. 事件处理竞争")
    print("3. GLib主循环阻塞")
    print("需要实现协调机制来解决这个问题")

if __name__ == "__main__":
    main()
