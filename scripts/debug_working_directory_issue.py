#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试工作目录对中断机制的影响

作者: GAT开发团队
日期: 2025年1月4日
"""

import os
import sys
import subprocess
import time

def analyze_working_directory_impact():
    """分析工作目录对中断机制的影响"""
    print("🔍 分析工作目录对中断机制的影响")
    print("="*60)
    
    # 测试场景
    scenarios = [
        {
            'name': 'scripts/recorder目录执行 (正常中断)',
            'cwd': '/home/<USER>/kylin-robot-ide/scripts/recorder',
            'cmd': ['python3', 'run_recorder.py', '--debug', '--duration', '8']
        },
        {
            'name': 'scripts目录执行 (中断失效)',
            'cwd': '/home/<USER>/kylin-robot-ide/scripts',
            'cmd': ['python3', 'recorder/run_recorder.py', '--debug', '--duration', '8']
        },
        {
            'name': '根目录执行 (测试)',
            'cwd': '/home/<USER>/kylin-robot-ide',
            'cmd': ['python3', 'scripts/recorder/run_recorder.py', '--debug', '--duration', '8']
        }
    ]
    
    for scenario in scenarios:
        print(f"\n🔵 {scenario['name']}")
        print(f"   工作目录: {scenario['cwd']}")
        print(f"   命令: {' '.join(scenario['cmd'])}")
        
        if not os.path.exists(scenario['cwd']):
            print(f"   ❌ 目录不存在")
            continue
        
        try:
            start_time = time.time()
            
            # 启动进程
            process = subprocess.Popen(
                scenario['cmd'],
                cwd=scenario['cwd'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            print(f"   🚀 进程已启动，PID: {process.pid}")
            
            # 监控6秒
            monitor_duration = 6
            interrupt_detected = False
            timeout_detected = False
            recognition_count = 0
            
            while time.time() - start_time < monitor_duration:
                if process.poll() is not None:
                    print(f"   ✅ 进程已结束，返回码: {process.returncode}")
                    break
                
                # 读取输出
                try:
                    import select
                    ready, _, _ = select.select([process.stdout, process.stderr], [], [], 0.1)
                    
                    if process.stderr in ready:
                        line = process.stderr.readline()
                        if line:
                            line = line.strip()
                            print(f"   📥 {line}")
                            
                            # 检测关键信息
                            if '控件识别被中断' in line:
                                interrupt_detected = True
                                print(f"   ✅ 检测到中断机制工作！")
                            elif '开始控件识别' in line:
                                recognition_count += 1
                            elif '控件识别耗时' in line or '识别超时' in line:
                                timeout_detected = True
                                print(f"   ⚠️  检测到超时")
                
                except Exception:
                    break
                
                time.sleep(0.1)
            
            # 终止进程
            if process.poll() is None:
                print(f"   ⏰ 监控时间结束，终止进程...")
                process.terminate()
                try:
                    process.wait(timeout=3)
                except subprocess.TimeoutExpired:
                    process.kill()
                    process.wait()
            
            execution_time = time.time() - start_time
            
            print(f"   📊 执行时间: {execution_time:.3f}秒")
            print(f"   ✅ 中断检测: {interrupt_detected}")
            print(f"   🔍 识别次数: {recognition_count}")
            print(f"   ⚠️  超时检测: {timeout_detected}")
            
            # 分析结果
            if interrupt_detected:
                print(f"   🎉 中断机制正常工作")
            elif timeout_detected:
                print(f"   ❌ 中断机制失效，出现超时")
            else:
                print(f"   ℹ️  无识别事件或正常结束")
            
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")

def analyze_path_resolution():
    """分析路径解析差异"""
    print(f"\n🔍 分析路径解析差异")
    print("="*40)
    
    # 创建路径分析脚本
    path_analysis_script = '''
import sys
import os

print(f"=== 路径分析 ===", file=sys.stderr)
print(f"sys.argv[0]: {sys.argv[0]}", file=sys.stderr)
print(f"__file__: {__file__}", file=sys.stderr)
print(f"工作目录: {os.getcwd()}", file=sys.stderr)
print(f"脚本绝对路径: {os.path.abspath(__file__)}", file=sys.stderr)
print(f"脚本目录: {os.path.dirname(os.path.abspath(__file__))}", file=sys.stderr)

# 计算项目根目录
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
print(f"计算的项目根目录: {project_root}", file=sys.stderr)

# 检查Python路径
print(f"Python路径前5项:", file=sys.stderr)
for i, path in enumerate(sys.path[:5]):
    print(f"  [{i}] {path}", file=sys.stderr)

# 检查模块导入路径
try:
    import threading
    print(f"threading模块路径: {threading.__file__}", file=sys.stderr)
except Exception as e:
    print(f"threading模块导入失败: {e}", file=sys.stderr)

print("=" * 50, file=sys.stderr)
'''
    
    # 测试不同工作目录的路径解析
    test_scenarios = [
        {
            'name': 'scripts/recorder目录',
            'cwd': '/home/<USER>/kylin-robot-ide/scripts/recorder',
            'script': 'path_test.py',
            'cmd': ['python3', 'path_test.py']
        },
        {
            'name': 'scripts目录',
            'cwd': '/home/<USER>/kylin-robot-ide/scripts',
            'script': 'recorder/path_test.py',
            'cmd': ['python3', 'recorder/path_test.py']
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n🔹 {scenario['name']}:")
        
        script_path = os.path.join(scenario['cwd'], scenario['script'])
        script_dir = os.path.dirname(script_path)
        
        try:
            # 确保目录存在
            os.makedirs(script_dir, exist_ok=True)
            
            # 创建测试脚本
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(path_analysis_script)
            
            # 执行测试
            result = subprocess.run(
                scenario['cmd'],
                cwd=scenario['cwd'],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.stderr:
                for line in result.stderr.split('\n'):
                    if line.strip():
                        print(f"   {line}")
            
            # 清理
            os.remove(script_path)
        
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")

def analyze_module_import_differences():
    """分析模块导入差异"""
    print(f"\n🔍 分析模块导入差异")
    print("="*40)
    
    # 检查UNI模块导入
    import_test_script = '''
import sys
import os
import time

print(f"工作目录: {os.getcwd()}", file=sys.stderr)

# 尝试导入UNI模块
try:
    start_time = time.time()
    import UNI
    import_time = time.time() - start_time
    print(f"UNI模块导入成功，耗时: {import_time:.6f}秒", file=sys.stderr)
    print(f"UNI模块路径: {UNI.__file__}", file=sys.stderr)
except Exception as e:
    print(f"UNI模块导入失败: {e}", file=sys.stderr)

# 尝试导入其他关键模块
modules_to_test = ['threading', 'queue', 'time']
for module_name in modules_to_test:
    try:
        start_time = time.time()
        module = __import__(module_name)
        import_time = time.time() - start_time
        print(f"{module_name}模块导入耗时: {import_time:.6f}秒", file=sys.stderr)
    except Exception as e:
        print(f"{module_name}模块导入失败: {e}", file=sys.stderr)
'''
    
    test_scenarios = [
        ('scripts/recorder目录', '/home/<USER>/kylin-robot-ide/scripts/recorder'),
        ('scripts目录', '/home/<USER>/kylin-robot-ide/scripts')
    ]
    
    for name, cwd in test_scenarios:
        print(f"\n🔹 {name}:")
        
        script_path = os.path.join(cwd, 'import_test.py')
        
        try:
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(import_test_script)
            
            result = subprocess.run(
                ['python3', 'import_test.py'],
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.stderr:
                for line in result.stderr.split('\n'):
                    if line.strip():
                        print(f"   {line}")
            
            os.remove(script_path)
        
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")

def main():
    print("🔍 GAT录制脚本工作目录影响分析")
    print("="*60)
    
    print("📋 问题描述:")
    print("• scripts/recorder目录执行: python3 run_recorder.py → 中断正常")
    print("• scripts目录执行: python3 recorder/run_recorder.py → 中断失效")
    print("• 需要找出工作目录影响中断机制的原因")
    
    # 分析工作目录影响
    analyze_working_directory_impact()
    
    # 分析路径解析差异
    analyze_path_resolution()
    
    # 分析模块导入差异
    analyze_module_import_differences()
    
    print(f"\n💡 可能的原因:")
    print("1. 不同工作目录导致模块导入路径差异")
    print("2. 相对路径解析的差异影响模块加载")
    print("3. Python路径设置的差异")
    print("4. 线程创建和管理的环境差异")
    print("5. 文件系统访问权限的差异")

if __name__ == "__main__":
    main()
