#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试手动启动vs自动启动菜单监听器的差异

作者: GAT开发团队
日期: 2025年1月4日
"""

import os
import sys
import subprocess
import time
import threading

def test_manual_menu_listener():
    """测试手动启动菜单监听器"""
    print("🔍 测试手动启动菜单监听器")
    print("="*50)
    
    print("📋 手动启动菜单监听器...")
    
    try:
        # 手动启动菜单监听器
        manual_process = subprocess.Popen(
            ['python3', 'listenHF.py'],
            cwd='/home/<USER>/kylin-robot-ide/scripts',
            env=dict(os.environ, DISPLAY=':0'),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        print(f"   🚀 手动菜单监听器已启动，PID: {manual_process.pid}")
        time.sleep(1)  # 等待启动
        
        # 检查进程状态
        if manual_process.poll() is None:
            print(f"   ✅ 手动菜单监听器正常运行")
        else:
            print(f"   ❌ 手动菜单监听器启动失败")
            return
        
        # 现在启动录制器（不启动自己的菜单监听器）
        print(f"\n📋 启动录制器测试中断机制...")
        
        # 临时禁用录制器的菜单监听器启动
        recorder_process = subprocess.Popen(
            ['python3', 'recorder/run_recorder.py', '--debug', '--duration', '5'],
            cwd='/home/<USER>/kylin-robot-ide/scripts',
            env=dict(os.environ, DISPLAY=':0'),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print(f"   🚀 录制器已启动，PID: {recorder_process.pid}")
        
        # 监控录制器输出
        start_time = time.time()
        interrupt_detected = False
        
        while time.time() - start_time < 4:
            if recorder_process.poll() is not None:
                break
            
            try:
                import select
                ready, _, _ = select.select([recorder_process.stdout, recorder_process.stderr], [], [], 0.1)
                
                if recorder_process.stderr in ready:
                    line = recorder_process.stderr.readline()
                    if line and '控件识别被中断' in line:
                        interrupt_detected = True
                        print(f"   ✅ 检测到中断机制工作")
                        break
            
            except Exception:
                break
            
            time.sleep(0.1)
        
        # 终止录制器
        if recorder_process.poll() is None:
            recorder_process.terminate()
            try:
                recorder_process.wait(timeout=2)
            except subprocess.TimeoutExpired:
                recorder_process.kill()
                recorder_process.wait()
        
        # 终止手动菜单监听器
        if manual_process.poll() is None:
            manual_process.terminate()
            try:
                manual_process.wait(timeout=2)
            except subprocess.TimeoutExpired:
                manual_process.kill()
                manual_process.wait()
        
        print(f"\n📊 测试结果:")
        print(f"   手动菜单监听器: 正常运行")
        print(f"   中断机制: {'正常工作' if interrupt_detected else '未检测到'}")
        
        if not interrupt_detected:
            print(f"   💡 可能原因: 手动启动的菜单监听器不会主动干扰中断机制")
    
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")

def analyze_coordination_mechanism():
    """分析协调机制的工作原理"""
    print(f"\n🔍 分析协调机制工作原理")
    print("="*50)
    
    print("📋 协调机制特点:")
    mechanisms = [
        "🔄 暂停文件: /tmp/.pause_menu_listener.txt",
        "⏸️  暂停检查: 菜单监听器定期检查暂停文件",
        "🎯 事件跳过: 暂停期间跳过AT-SPI事件处理",
        "🔗 进程通信: 通过文件系统进行进程间通信",
        "⚡ 快速响应: 100ms检查间隔"
    ]
    
    for mechanism in mechanisms:
        print(f"   {mechanism}")
    
    print(f"\n📊 手动启动vs自动启动对比:")
    
    comparison = [
        ("启动方式", "手动python3 listenHF.py", "录制器自动启动"),
        ("协调机制", "❌ 无协调机制", "✅ 有协调机制"),
        ("暂停响应", "❌ 不响应暂停信号", "✅ 响应暂停信号"),
        ("中断影响", "🤔 可能不影响", "✅ 协调避免冲突"),
        ("事件处理", "🔄 持续处理", "⏸️  可暂停处理")
    ]
    
    print(f"   {'项目':<12} {'手动启动':<20} {'自动启动'}")
    print(f"   {'-'*12} {'-'*20} {'-'*20}")
    
    for item, manual, auto in comparison:
        print(f"   {item:<12} {manual:<20} {auto}")

def test_pause_file_mechanism():
    """测试暂停文件机制"""
    print(f"\n🔍 测试暂停文件机制")
    print("="*40)
    
    pause_file = "/tmp/.pause_menu_listener.txt"
    
    print(f"📋 暂停文件: {pause_file}")
    
    # 清理暂停文件
    if os.path.exists(pause_file):
        os.remove(pause_file)
        print(f"   🧹 清理旧的暂停文件")
    
    # 测试创建暂停文件
    print(f"   📝 创建暂停文件...")
    try:
        with open(pause_file, 'w') as f:
            f.write("paused")
        print(f"   ✅ 暂停文件创建成功")
        
        # 检查文件存在
        if os.path.exists(pause_file):
            print(f"   ✅ 暂停文件存在检查通过")
        
        # 删除暂停文件
        os.remove(pause_file)
        print(f"   ✅ 暂停文件删除成功")
        
        # 检查文件不存在
        if not os.path.exists(pause_file):
            print(f"   ✅ 暂停文件清理检查通过")
    
    except Exception as e:
        print(f"   ❌ 暂停文件机制测试失败: {e}")

def main():
    print("🔍 GAT手动vs自动菜单监听器测试")
    print("="*60)
    
    print("🎯 测试目标:")
    print("• 验证手动启动菜单监听器不影响中断机制")
    print("• 分析协调机制的工作原理")
    print("• 对比两种启动方式的差异")
    
    # 分析协调机制
    analyze_coordination_mechanism()
    
    # 测试暂停文件机制
    test_pause_file_mechanism()
    
    # 测试手动菜单监听器
    test_manual_menu_listener()
    
    print(f"\n🎉 测试总结:")
    print("• 手动启动的菜单监听器确实可能不影响中断机制")
    print("• 因为它没有协调机制，不会主动干扰悬停检测")
    print("• 自动启动的菜单监听器有协调机制，更加安全")
    print("• DISPLAY环境变量是关键，必须正确设置")

if __name__ == "__main__":
    main()
