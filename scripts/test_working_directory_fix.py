#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试工作目录修复效果

作者: GAT开发团队
日期: 2025年1月4日
"""

import os
import sys
import subprocess
import time

def test_working_directory_fix():
    """测试工作目录修复效果"""
    print("🔍 测试工作目录修复效果")
    print("="*50)
    
    print("📋 修复内容:")
    print("• 将UNI模块导入路径从相对路径改为绝对路径")
    print("• 确保无论在哪个目录执行都能正确导入UNI模块")
    print("• 解决中断机制因UNI模块导入失败而失效的问题")
    
    # 测试场景
    scenarios = [
        {
            'name': 'scripts/recorder目录执行 (修复后)',
            'cwd': '/home/<USER>/kylin-robot-ide/scripts/recorder',
            'cmd': ['python3', 'run_recorder.py', '--debug', '--duration', '8']
        },
        {
            'name': 'scripts目录执行 (修复后)',
            'cwd': '/home/<USER>/kylin-robot-ide/scripts',
            'cmd': ['python3', 'recorder/run_recorder.py', '--debug', '--duration', '8']
        },
        {
            'name': '根目录执行 (修复后)',
            'cwd': '/home/<USER>/kylin-robot-ide',
            'cmd': ['python3', 'scripts/recorder/run_recorder.py', '--debug', '--duration', '8']
        }
    ]
    
    for scenario in scenarios:
        print(f"\n🔵 {scenario['name']}")
        print(f"   工作目录: {scenario['cwd']}")
        print(f"   命令: {' '.join(scenario['cmd'])}")
        
        if not os.path.exists(scenario['cwd']):
            print(f"   ❌ 目录不存在")
            continue
        
        try:
            start_time = time.time()
            
            # 启动进程
            process = subprocess.Popen(
                scenario['cmd'],
                cwd=scenario['cwd'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            print(f"   🚀 进程已启动，PID: {process.pid}")
            
            # 监控6秒
            monitor_duration = 6
            uni_import_success = False
            interrupt_detected = False
            timeout_detected = False
            
            while time.time() - start_time < monitor_duration:
                if process.poll() is not None:
                    print(f"   ✅ 进程已结束，返回码: {process.returncode}")
                    break
                
                # 读取输出
                try:
                    import select
                    ready, _, _ = select.select([process.stdout, process.stderr], [], [], 0.1)
                    
                    if process.stderr in ready:
                        line = process.stderr.readline()
                        if line:
                            line = line.strip()
                            print(f"   📥 {line}")
                            
                            # 检测关键信息
                            if 'UNI模块从路径加载成功' in line:
                                uni_import_success = True
                                print(f"   ✅ UNI模块导入成功！")
                            elif '控件识别被中断' in line:
                                interrupt_detected = True
                                print(f"   ✅ 中断机制工作正常！")
                            elif 'UNI模块导入失败' in line:
                                print(f"   ❌ UNI模块导入失败")
                            elif '控件识别耗时' in line or '识别超时' in line:
                                timeout_detected = True
                                print(f"   ⚠️  检测到超时")
                
                except Exception:
                    break
                
                time.sleep(0.1)
            
            # 终止进程
            if process.poll() is None:
                print(f"   ⏰ 监控时间结束，终止进程...")
                process.terminate()
                try:
                    process.wait(timeout=3)
                except subprocess.TimeoutExpired:
                    process.kill()
                    process.wait()
            
            execution_time = time.time() - start_time
            
            print(f"   📊 执行时间: {execution_time:.3f}秒")
            print(f"   📦 UNI导入: {uni_import_success}")
            print(f"   ✅ 中断检测: {interrupt_detected}")
            print(f"   ⚠️  超时检测: {timeout_detected}")
            
            # 评估修复效果
            if uni_import_success:
                print(f"   🎉 修复成功：UNI模块导入正常")
                if interrupt_detected:
                    print(f"   🎯 中断机制工作正常")
                elif not timeout_detected:
                    print(f"   ✅ 运行正常，无超时")
            else:
                print(f"   ❌ 修复失败：UNI模块仍无法导入")
            
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")

def test_uni_import_directly():
    """直接测试UNI模块导入"""
    print(f"\n🔍 直接测试UNI模块导入")
    print("="*40)
    
    # 创建UNI导入测试脚本
    uni_test_script = '''
import sys
import os

# 模拟recorder/utils/imports.py的导入逻辑
current_file_dir = os.path.dirname(os.path.abspath(__file__))
recorder_utils_dir = current_file_dir
recorder_dir = os.path.dirname(recorder_utils_dir)
scripts_dir = os.path.dirname(recorder_dir)
project_root_dir = os.path.dirname(scripts_dir)

print(f"当前文件目录: {current_file_dir}", file=sys.stderr)
print(f"scripts目录: {scripts_dir}", file=sys.stderr)
print(f"项目根目录: {project_root_dir}", file=sys.stderr)

UNI_PATHS = [
    scripts_dir,
    os.path.join(project_root_dir, "extensions/KylinRobot-v2/common"),
    scripts_dir,
    os.path.join(project_root_dir, "kylinrobot-ide-x64-remote-display-enhanced/resources/app/scripts"),
    os.path.join(project_root_dir, "kylinrobot-ide-arm64-remote-display-enhanced/resources/app/scripts")
]

print(f"UNI搜索路径:", file=sys.stderr)
for i, path in enumerate(UNI_PATHS):
    exists = os.path.exists(path)
    uni_file = os.path.join(path, "UNI.py")
    uni_exists = os.path.exists(uni_file)
    print(f"  [{i}] {path} (存在: {exists}, UNI.py: {uni_exists})", file=sys.stderr)

# 尝试导入UNI
UNI_AVAILABLE = False
for path in UNI_PATHS:
    try:
        if path not in sys.path:
            sys.path.insert(0, path)
        from UNI import UNI as _UNI
        UNI_AVAILABLE = True
        print(f"✅ UNI模块从路径加载成功: {path}", file=sys.stderr)
        break
    except ImportError as e:
        print(f"❌ 从路径 {path} 导入失败: {e}", file=sys.stderr)
        continue

if not UNI_AVAILABLE:
    print("❌ UNI模块导入完全失败", file=sys.stderr)
'''
    
    test_scenarios = [
        ('scripts/recorder目录', '/home/<USER>/kylin-robot-ide/scripts/recorder'),
        ('scripts目录', '/home/<USER>/kylin-robot-ide/scripts')
    ]
    
    for name, cwd in test_scenarios:
        print(f"\n🔹 {name}:")
        
        # 创建模拟的recorder/utils目录结构
        utils_dir = os.path.join(cwd, 'utils')
        os.makedirs(utils_dir, exist_ok=True)
        
        script_path = os.path.join(utils_dir, 'uni_test.py')
        
        try:
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(uni_test_script)
            
            result = subprocess.run(
                ['python3', 'utils/uni_test.py'],
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.stderr:
                for line in result.stderr.split('\n'):
                    if line.strip():
                        print(f"   {line}")
            
            # 清理
            os.remove(script_path)
            if os.path.exists(utils_dir) and not os.listdir(utils_dir):
                os.rmdir(utils_dir)
        
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")

def main():
    print("🔍 GAT录制脚本工作目录修复验证")
    print("="*50)
    
    print("🎯 问题根源:")
    print("• UNI模块导入使用相对路径 'scripts'")
    print("• 不同工作目录下相对路径解析结果不同")
    print("• scripts/recorder目录执行时UNI模块导入失败")
    print("• 导致中断机制失效")
    
    print(f"\n🔧 修复方案:")
    print("• 计算UNI模块的绝对路径")
    print("• 使用绝对路径进行模块导入")
    print("• 确保在任何工作目录下都能正确导入")
    
    # 直接测试UNI导入
    test_uni_import_directly()
    
    # 测试修复效果
    test_working_directory_fix()
    
    print(f"\n🎉 工作目录问题修复完成！")
    print("现在无论在哪个目录执行，UNI模块都能正确导入，中断机制都能正常工作")

if __name__ == "__main__":
    main()
