# 编辑 Action 功能实现方案

## 背景
当前在测试用例编辑器中，用户可通过右键插入公共方法；但无法对已存在的 `action` 块进行右键编辑。

## 目标
- 在 `action` 块内部右键时，显示“编辑 Action”菜单项。
- 打开「插入公共方法」窗口，预填选中的方法名称和参数列表。
- 修改后，将新的参数内容替换原 `action` 块。

## 实现步骤

1. 定义上下文键
   ```ts
   const ctxIsActionBlock = new RawContextKey<boolean>('gat:isActionBlock', false, '是否在 action 块中');
   ```
   > **备注：需要调整此处实现逻辑**

## 后续计划（暂存）

2. 实现 `ActionBlockContextContribution`
   - 监听 `onDidChangeCursorPosition`、`onDidChangeModel`。
   - 在 `updateContext()` 中：
     ```ts
     const model = editor.getModel();
     const pos = editor.getPosition();
     if (!model||!pos) { ctxIsActionBlock.set(false); return; }
     // 向上查找最近以“- action:”开头的行，且未遇到其他步骤或 action
     for (let i = pos.lineNumber; i >= 1; i--) {
       const line = model.getLineContent(i).trim();
       if (line.startsWith('- action:')) { ctxIsActionBlock.set(true); return; }
       if (/^- /.test(line) && i < pos.lineNumber) { break; }
     }
     ctxIsActionBlock.set(false);
     ```

3. 注册编辑器贡献
   ```ts
   registerEditorContribution(
     ActionBlockContextContribution.ID,
     ActionBlockContextContribution,
     EditorContributionInstantiation.Eager
   );
   ```

4. 注册 `gat.editAction` 命令
   - 获取当前编辑器、光标位置，解析出选中 `action` 行和参数块的起止行。
   - 从行内容中提取 `methodName` 和 `parameters`。
   - 创建 `InsertMethodWindow`，调用 `setEditContext(methodName, parameters)`。
   - 在 `onMethodInserted` 回调中，调用新方法 `editActionToTestCase(...)`。

5. 在 Robot 子菜单添加“编辑 Action”项
   ```ts
   MenuRegistry.appendMenuItem(GAT_EDITOR_CONTEXT_MENU, {
     command: { id: 'gat.editAction', title: '编辑 Action' },
     group: '1_gat', order: 0,
     when: ContextKeyExpr.equals('gat:isActionBlock', true)
   });
   ```

6. 扩展 `MethodInsertionHandler`
   - 添加方法 `async editActionToTestCase(methodName, formattedCode, targetSection)`：
     1. 读取并按行拆分测试用例文件。
     2. 调用 `findTargetSectionRange` 定位当前 `action` 块起止行及缩进。
     3. 使用新的 `formattedCode` 替换原有内容。
     4. 写回文件并发送通知。

7. 扩展 `InsertMethodWindow`
   - 增加 `setEditContext(methodName: string, parameters: Record<string, any>)` 接口。
   - 在窗口打开时，调用 `eventHandler.updateMethodDetails(doc, docOfSelectedMethod, parameters)` 预填界面。

---

请团队审阅该方案，有任何补充或修改建议，可在此文档中记录。
